import type { AskMessage } from '@/common/services/base-chat/types';
import type { ClientMessage } from '@/conversation/client-message/abstract-message';
import { createDecorator } from '@byted-image/lv-bedrock/di';
import type { Event } from '@byted-image/lv-bedrock/event';

export interface IPrivateChatService {
  _serviceBrand: undefined;

  sendMessage(message: AskMessage): void;

  getMessages(): ClientMessage[];

  onPresentAssistantMessage: Event<[]>;
}

export const IPrivateChatService = createDecorator<IPrivateChatService>('private-chat-service');
