import os from 'node:os';
import * as path from 'node:path';
import * as vscode from 'vscode';

export class CwdProvider {
  public readonly cwd: string;

  constructor() {
    this.cwd =
      // may or may not exist but fs checking existence would immediately ask for permission which would be bad UX, need to come up with a better solution
      vscode.workspace.workspaceFolders?.map((folder) => folder.uri.fsPath).at(0) ?? path.join(os.homedir(), 'Desktop');
  }
}
