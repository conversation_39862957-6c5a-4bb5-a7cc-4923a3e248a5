import { Controller } from '@/controller/controller';
import type { IInstantiationService } from '@byted-image/lv-bedrock/di';
import { Uri, Webview, WebviewPanel, WebviewView, WebviewViewProvider } from 'vscode';
import { getNonce } from '../utilities/get-nonce';
import { getUri } from '../utilities/get-uri';

export class WebviewProvider implements WebviewViewProvider {
  public static readonly viewType = 'codin.chatView';
  public static readonly tabPanelId = 'codin.TabPanelProvider';
  private readonly _controller: Controller;
  public view?: WebviewView | WebviewPanel;

  constructor(
    private readonly _extensionUri: Uri,
    private readonly _instantiationService: IInstantiationService,
  ) {
    this._controller = new Controller();
  }

  public resolveWebviewView(webviewView: WebviewView | WebviewPanel) {
    this.view = webviewView;

    // 使用共享的DI容器初始化Controller
    this._controller.initDI(this._instantiationService);
    // 设置 webview 给 controller 用于 rpc
    this._controller.setWebview(webviewView.webview);

    // Allow scripts in the webview
    webviewView.webview.options = {
      // Enable JavaScript in the webview
      enableScripts: true,
      // @ts-expect-error
      retainContextWhenHidden: true,
      // Restrict the webview to only load resources from the `out` directory
      localResourceRoots: [Uri.joinPath(this._extensionUri, 'out')],
    };

    // Set the HTML content that will fill the webview view
    webviewView.webview.html = this._getWebviewContent(webviewView.webview, this._extensionUri);
  }

  private _getWebviewContent(webview: Webview, extensionUri: Uri) {
    const webviewUri = getUri(webview, extensionUri, ['out', 'webview.js']);
    const stylesUri = getUri(webview, extensionUri, ['out', 'webview.css']);
    const nonce = getNonce();

    return /*html*/ `
      <!DOCTYPE html>
      <html lang="en">
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <meta http-equiv="Content-Security-Policy" content="default-src 'none'; style-src ${webview.cspSource} 'unsafe-inline'; script-src 'nonce-${nonce}'; img-src 'self' data: https:;">
          <link rel="stylesheet" href="${stylesUri}">
          <title>Codin Agent</title>
        </head>
        <body>
          <div id="root"></div>
          <script type="module" nonce="${nonce}" src="${webviewUri}"></script>
        </body>
      </html>
    `;
  }
}
