import type { Int64 } from '@/bam';
import { DataType, type ToolInfo } from '@/bam/namespaces/agentclient';
import type { IDisposable } from '@byted-image/lv-bedrock/dispose';
import { Tool, Tool<PERSON><PERSON>ult, ToolResultStatus } from '../base';
import ask<PERSON><PERSON>Interactor from './ask-user-interactor';
import { questionType, toolDesc, toolName } from './tool-info';
import { IStorageService } from '@/common/services/storage/storage-service.interface';

export class AskUserTool extends Tool {
  constructor(@IStorageService private readonly _storageService: IStorageService) {
    super();
  }

  getInfo(): ToolInfo {
    return {
      name: toolName,
      desc: toolDesc,
      params: {
        explanation: {
          type: DataType.String,
          desc: 'One sentence explanation as to why this tool is being used, and how it contributes to the goal.',
          required: false,
        },
        questions: {
          type: DataType.Array,
          desc: 'The questions to ask the user',
          required: true,
          elem_info: {
            type: DataType.Object,
            desc: 'The question to ask the user',
            sub_params: {
              question_uuid: {
                type: DataType.String,
                desc: 'The uuid of the question',
                required: true,
              },
              question_content: {
                type: DataType.String,
                desc: 'The content of the question',
                required: true,
              },
              question_type: {
                type: DataType.String,
                desc: 'The types of questions. Here, "Text" represents questions requiring manual text input, "Radio" denotes single-choice questions, and "Checkbox" indicates multiple-choice questions.',
                enum: questionType as unknown as string[],
                required: true,
              },
              selection_options: {
                type: DataType.Array,
                desc: 'The options of the question. Only valid when the question type is "Radio" or "Checkbox"',
                required: false,
                elem_info: {
                  type: DataType.String,
                  desc: 'The option of the question. Only valid when the question type is "Radio" or "Checkbox"',
                  required: true,
                },
              },
            },
            required: true,
          },
        },
      },
    };
  }

  async run(inputStr: string, toolId: string, _conversationId: string, _version: Int64): Promise<ToolResult> {
    try {
      // 验证inputStr是否为有效的JSON
      const input = JSON.parse(inputStr);
      if (!Array.isArray(input.questions)) {
        return new ToolResult(
          ToolResultStatus.AutoRunError,
          'Ask user parameter parse error: questions is not an Array.',
        );
      }

      const answer = await this._waitUserConfirmAnswer(toolId);
      console.log('ask-user.run', answer);
      return new ToolResult(ToolResultStatus.AutoRunSuccess, answer);
    } catch (error) {
      // 返回错误信息字符串
      if (error instanceof Error) {
        return new ToolResult(ToolResultStatus.AutoRunError, error.message);
      }
      return new ToolResult(ToolResultStatus.AutoRunError, String(error));
    }
  }

  private async _waitUserConfirmAnswer(toolId: string): Promise<string> {
    const toolAutoRun = await this._storageService.get('toolAutoRun');
    if (toolAutoRun === 'true') {
      return '当前环境中，用户无法与你交互，无法提供任何问题答案，请你根据收集的信息，继续完成任务。';
    }

    return new Promise<string>((resolve) => {
      let disposable: IDisposable | null = askUserInteractor.onConfirmAnswer((id, answer) => {
        if (id === toolId) {
          disposable?.dispose();
          disposable = null;
          resolve(answer);
        }
      });
    });
  }
}
