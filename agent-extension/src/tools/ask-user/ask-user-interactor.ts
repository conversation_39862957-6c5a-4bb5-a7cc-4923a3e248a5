import { Emitter, Event } from '@byted-image/lv-bedrock/event';

/**
 * 询问用户交互层
 */
class AskUserInteractor {
  public onConfirmAnswer: Event<[string, string]>;

  private readonly _onConfirmAnswer = new Emitter<[string, string]>();

  constructor() {
    this.onConfirmAnswer = this._onConfirmAnswer.event;
  }

  confirmAnswer(toolId: string, answer: string) {
    this._onConfirmAnswer.fire(toolId, answer);
  }
}

export default new AskUserInteractor();
