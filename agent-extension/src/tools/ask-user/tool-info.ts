export const questionType = ['Text', 'Radio', 'Checkbox'] as const;

export type Question = {
  question_uuid: string;
  question_content: string;
  question_type: (typeof questionType)[number];
  selection_options?: string[];
};

export type Input = {
  explanation?: string;
  questions: Question[];
};

export const toolName = 'ask_user';

export const toolDesc = `This tool is your ONLY means to inquire of users.
You should use it when needing to request users to provide additional context, when asking whether users require further tasks, or when posing any questions.
You MUST ask questions progressively, with no more than five at a time. When you need to ask more than five questions, please do so in batches.
You should not overly rely on this tool; always attempt to resolve issues independently first.`;
