import type { Int64, ToolInfo } from '@/bam/namespaces/agentclient';
import { DataType } from '@/bam/namespaces/agentclient';
import { PromptItemType } from '@/bam/namespaces/userinput';
import { AskMessageRole } from '@/common/services/base-chat/types';
import { ConversationStatus } from '@/common/services/conversation/base-conversation-service';
import { ID2cChatService } from '@/common/services/d2c-chat/chat-service.interface';
import { ClientContentMessage } from '@/conversation/client-message/content-message';
import { ToolResult, ToolResultStatus, Tool } from '../base';
import { toolDesc, toolName } from './tool-info';
import { listenOnce } from '@byted-image/lv-bedrock/event';
import type { BaseConversationContext } from '@/common/services/conversation/base-conversation-context/base-conversation-context';

export class DesignDetailTool extends Tool {
  constructor(@ID2cChatService private readonly _d2cChatService: ID2cChatService) {
    super();
  }

  getInfo(): ToolInfo {
    return {
      name: toolName,
      desc: toolDesc,
      params: {
        link: {
          type: DataType.String,
          desc: 'The link of figma design draft like "https://www.figma.com/design/aaa?node-id=xxx"',
          required: false,
        },
      },
    };
  }

  async run(inputStr: string, _toolId: string, conversationId: string, version: Int64): Promise<ToolResult> {
    try {
      console.log('Design detail run', inputStr);
      const input = JSON.parse(inputStr);
      const link = input.link;

      const [error, subCid] = (
        await this._d2cChatService.createConversation({
          parentConversationId: conversationId,
          parentMessageVersion: version,
        })
      ).pair();

      if (error) {
        return new ToolResult(ToolResultStatus.AutoRunError, `Technical planning error: ${error.msg}`);
      }
      const context = this._d2cChatService.getConversationContext(subCid);
      if (!context) {
        return new ToolResult(ToolResultStatus.AutoRunError, 'Technical planning error: no conversation context');
      }

      this._d2cChatService.sendMessage({
        cid: subCid,
        role: AskMessageRole.User,
        userContent: {
          prompt: {
            items: [
              [
                {
                  type: PromptItemType.Text,
                  text: {
                    text: `帮我获取设计稿 ${link} 的详细信息`,
                  },
                },
              ],
            ],
          },
        },
      });

      const result = await Promise.race([
        this._waitSendMessageError().then((errorMessage) => {
          return new ToolResult(ToolResultStatus.AutoRunError, errorMessage);
        }),
        this._waitMessageEnd(context).then((message) => {
          return new ToolResult(ToolResultStatus.AutoRunSuccess, message);
        }),
      ]);

      return result;
    } catch (error) {
      return new ToolResult(ToolResultStatus.AutoRunError, `Design analysis error: ${String(error)}`);
    }
  }

  private async _waitMessageEnd(context: BaseConversationContext): Promise<string> {
    return new Promise((resolve) => {
      context.messageReceiver.onStatusChange((status) => {
        if (status === ConversationStatus.Idle) {
          const messages = context.messagesManager.getMessages();
          const lastMessage = messages[messages.length - 1] as ClientContentMessage;
          console.log('Design analysis waitMessageEnd', lastMessage);

          if (!lastMessage) {
            resolve('Design analysis error: no message');
            return;
          }

          resolve(lastMessage.content);
        }
      });
    });
  }

  private async _waitSendMessageError(): Promise<string> {
    return new Promise((resolve) => {
      listenOnce(this._d2cChatService.onSendMessageError)((error) => {
        resolve(error.toString());
      });
    });
  }
}
