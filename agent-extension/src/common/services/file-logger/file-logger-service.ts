import { IInstantiationService } from '@byted-image/lv-bedrock/di';
import { defer } from '@byted-image/lv-bedrock/promise';
import pino, { type Logger, type LoggerOptions } from 'pino';

import { PinoRollStream } from './destination-streams/pino-roll';
import { VSCodeOutputChannelStream } from './destination-streams/output-channel';
import { type IFileLoggerService } from './file-logger-service.interface';
import type { IDestinationStream } from './destination-streams/destination-stream.interface';

/**
 * 文件日志服务实现
 */
export class FileLoggerService implements IFileLoggerService {
  public readonly _serviceBrand = undefined;

  private _logger: Logger | undefined;
  private _initDefer = defer();
  private _destinationStreams: IDestinationStream[] = [];

  constructor(@IInstantiationService private readonly _instantiationService: IInstantiationService) {
    this._init();
  }

  public initialize() {
    return this._initDefer.promise;
  }

  private async _init() {
    try {
      this._destinationStreams.push(
        this._instantiationService.createInstance(PinoRollStream),
        this._instantiationService.createInstance(VSCodeOutputChannelStream),
      );

      await Promise.all(this._destinationStreams.map((stream) => stream.initialize()));

      const loggerOptions: LoggerOptions = {
        level: 'info',
        timestamp: pino.stdTimeFunctions.isoTime,
      };

      this._logger = pino(
        loggerOptions,
        pino.multistream(this._destinationStreams.map((stream) => ({ stream, level: 'info' }))),
      );

      this._initDefer.resolve();
    } catch (error) {
      console.error('Failed to initialize file logger:', error);
      this._initDefer.reject(error);
    }
  }

  public info(message: string): void {
    this._logger!.info(message);
  }

  public warn(message: string): void {
    this._logger!.warn(message);
  }

  public error(message: string, error?: Error): void {
    this._logger!.error(error, message);
  }

  public dispose(): void {
    for (const stream of this._destinationStreams) {
      stream.dispose();
    }
  }
}
