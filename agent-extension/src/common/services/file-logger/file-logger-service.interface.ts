import { createDecorator } from '@byted-image/lv-bedrock/di';

/**
 * 文件日志服务接口
 */
export interface IFileLoggerService {
  _serviceBrand: undefined;

  initialize(): Promise<void>;

  /**
   * 记录一条信息级别的日志。
   * @param message 日志消息
   */
  info(message: string): void;

  /**
   * 记录一条警告级别的日志。
   * @param message 日志消息
   */
  warn(message: string): void;

  /**
   * 记录一条错误级别的日志。
   * @param message 错误描述
   * @param error 可选的 Error 对象，其堆栈信息将被记录
   */
  error(message: string, error?: Error): void;

  /**
   * 销毁 logger，释放文件句柄等资源。
   */
  dispose(): void;
}

export const IFileLoggerService = createDecorator<IFileLoggerService>('file-logger-service');
