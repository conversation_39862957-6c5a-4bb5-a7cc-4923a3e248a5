import { IInstantiationService } from '@byted-image/lv-bedrock/di';
import { type ILvErrorOr, makeErrorBy, makeOk, makeOkWith } from '@byted-image/lv-bedrock/error';
import { Emitter } from '@byted-image/lv-bedrock/event';

import { AgentId } from '@/common/services/conversation/const';
import { type ClientMessage } from '@/conversation/client-message/abstract-message';

import { AskMessage, type IConversationContextState, type IMessageChangeInfo } from '../base-chat/types';
import { INetworkClientFactoryService } from '../network-client-factory/network-client-factory-service.interface';
import { type IUnderstandingChatService } from './understanding-service.interface';
import type { Int64 } from '@/bam';
import type { CreateConversationRequest } from '@/bam/namespaces/agw';
import { ConversationTypeEnum } from '@/common/constants/conversation-types';
import { ConversationType } from '@/bam/namespaces/conversation';
import {
  IConversationHistoryService,
  type IConversationRelation,
} from '../conversation/history/conversation-history.interface';
import { UnderstandingConversationContext } from '../conversation/understanding-conversation-context/understanding-conversation';

export class UnderstandingChatService implements IUnderstandingChatService {
  private readonly _conversationType = ConversationTypeEnum.Understanding;
  static readonly AgentId = AgentId.Understanding;
  public _serviceBrand: undefined;
  public readonly _onUpdate = new Emitter<[]>();
  public readonly _onPresentAssistantMessage = new Emitter<[]>();
  public readonly _onSendMessageError = new Emitter<ILvErrorOr<void>[]>();
  private _networkClient = this._networkClientFactoryService.build({});
  private readonly _conversationIdToContextMap = new Map<string, UnderstandingConversationContext>();
  private _currentConversationId = '';
  private _onConversationContextChange = new Emitter<[IConversationContextState]>();
  private _onConversationMessageChange = new Emitter<[IMessageChangeInfo]>();
  get onConversationContextChange() {
    return this._onConversationContextChange.event;
  }
  get onConversationMessageChange() {
    return this._onConversationMessageChange.event;
  }
  constructor(
    @INetworkClientFactoryService
    private readonly _networkClientFactoryService: INetworkClientFactoryService,
    @IConversationHistoryService
    private readonly _historyService: IConversationHistoryService,
    @IInstantiationService
    private readonly _instantiationService: IInstantiationService,
  ) {}

  getMessages(): ClientMessage[] {
    return this.currentConversationContext?.messagesManager.getMessages() || [];
  }

  getCurrentContextState() {
    return this._makeConversationContextChangeData(this._currentConversationId);
  }

  public get onUpdate() {
    return this._onUpdate.event;
  }

  public get onPresentAssistantMessage() {
    return this._onPresentAssistantMessage.event;
  }

  public get onSendMessageError() {
    return this._onSendMessageError.event;
  }

  public get currentConversationContext() {
    return this._conversationIdToContextMap.get(this._currentConversationId);
  }

  resetMessageReceiverAndSocket() {}

  /** 创建会话的通用逻辑 */
  public async createConversation(options?: {
    parentConversationId: string;
    parentMessageVersion: Int64;
  }): Promise<ILvErrorOr<string>> {
    try {
      const { parentConversationId = '', parentMessageVersion = 0 } = options || {};
      const url = 'https://capcut-devops.byted.org/conversation/create';
      const headers = {};
      const data = this._getConversationData({
        parentConversationId,
        parentMessageVersion,
      });
      console.log(`[${this._conversationType}] 创建会话前: ${JSON.stringify(data)}`);
      const response = await this._networkClient(url, {
        method: 'POST',
        headers,
        data,
      });
      console.log(`[${this._conversationType}] 创建会话: ${JSON.stringify(response.headers)}`);
      const cid = response.data.data.id;
      await this._updateConversationRelation({
        cid,
        parentConversationId,
        parentMessageVersion,
      });
      const conversationContext = this._instantiationService.createInstance(
        UnderstandingConversationContext,
        {
          cid,
          parentCid: parentConversationId,
          parentMessageVersion,
        },
        [],
      );
      await conversationContext.bootstrap();
      this._conversationIdToContextMap.set(cid, conversationContext);
      console.log(`[${this._conversationType}] 创建会话成功: ${cid}, 类型: ${this._conversationType}`);
      return makeOkWith(cid);
    } catch (error) {
      console.log(`[${this._conversationType}] 创建会话失败: ${error}`);
      return makeErrorBy(-1, '创建会话失败', error as Error);
    }
  }

  public async restoreConversation(cid: string) {
    const conversationContext = this._conversationIdToContextMap.get(cid);
    if (conversationContext) {
      return makeOkWith(conversationContext);
    }
    const result = await this._restoreConversation(cid);
    if (!result.ok) {
      return result;
    }
    return result;
  }

  public async sendMessage(message: AskMessage) {
    const { cid } = message;

    if (!cid) {
      return makeErrorBy(-1, '发送消息失败', new Error('发送消息失败'));
    }

    const conversationContext = this._conversationIdToContextMap.get(cid);
    if (!conversationContext) {
      return makeErrorBy(-1, '发送消息失败', new Error('发送消息失败'));
    }
    conversationContext.sendMessage(message);
  }

  public async switchCurrentConversation(cid?: string) {
    if (cid) {
      const conversationContext = this._conversationIdToContextMap.get(cid);
      if (conversationContext) {
        this._currentConversationId = cid;
        const data = await this._makeConversationContextChangeData(this._currentConversationId);
        if (!data) {
          return makeErrorBy(-1, '切换会话失败', new Error('切换会话失败'));
        }
        this._onConversationContextChange.fire(data);
      } else {
        await this.restoreConversation(cid);
        const data = await this._makeConversationContextChangeData(this._currentConversationId);
        if (!data) {
          return makeErrorBy(-1, '切换会话失败', new Error('切换会话失败'));
        }
        this._onConversationContextChange.fire(data);
      }
    } else {
      const result = await this.createConversation();
      if (!result.ok) {
        return result;
      }
      this._currentConversationId = result.value;
      const data = await this._makeConversationContextChangeData(this._currentConversationId);
      if (!data) {
        return makeErrorBy(-1, '切换会话失败', new Error('切换会话失败'));
      }
      this._onConversationContextChange.fire(data);
    }

    return makeOk();
  }

  private async _restoreConversation(
    cid: string,
    parentId?: string,
    parentMessageVersion?: Int64,
  ): Promise<ILvErrorOr<IConversationContextState>> {
    const [historyRelation, historyMessage] = await Promise.all([
      this._historyService.getConversationRelation(cid),
      this._historyService.getConversationMessages(cid),
    ]);
    if (!historyRelation.ok || !historyMessage.ok) {
      return makeErrorBy(
        -1,
        '恢复会话失败',
        historyRelation.cause || historyMessage.cause || new Error('恢复会话失败'),
      );
    }
    // 接下来批量拉取子会话内容
    const messages = historyMessage.value;
    const conversationContext = this._instantiationService.createInstance(
      UnderstandingConversationContext,
      {
        cid,
        parentCid: parentId,
        parentMessageVersion,
      },
      messages,
    );
    const { childIds } = historyRelation.value as IConversationRelation;
    await Promise.all(
      childIds.map(async (item) => {
        return this._restoreConversation(item.cid, cid, item.parentMessageVersion);
      }),
    );
    await conversationContext.bootstrap();
    this._conversationIdToContextMap.set(cid, conversationContext);
    this._addMessageListener(conversationContext);
    return makeOk();
  }

  private _addMessageListener(context: UnderstandingConversationContext) {
    context.messagesManager.onAppendMessages((messages) => {
      if (context.id !== this._currentConversationId && context.parentId !== this._currentConversationId) {
        return;
      }
      const { id, parentId, parentMessageVersion } = context;
      this._onConversationMessageChange.fire({
        messages,
        type: 'add',
        cid: id,
        parentCid: parentId,
        parentMessageVersion,
      });
    });
    context.messagesManager.onUpdateMessage((message) => {
      if (context.id !== this._currentConversationId && context.parentId !== this._currentConversationId) {
        return;
      }
      const { id, parentId, parentMessageVersion } = context;
      this._onConversationMessageChange.fire({
        messages: [message],
        type: 'update',
        cid: id,
        parentCid: parentId,
        parentMessageVersion,
      });
    });
  }

  public getConversationContext(cid: string) {
    const conversationContext = this._conversationIdToContextMap.get(cid);
    if (!conversationContext) {
      return null;
    }
    return conversationContext;
  }

  private async _makeConversationContextChangeData(cid: string): Promise<IConversationContextState> {
    const conversationContext = this._conversationIdToContextMap.get(cid);
    if (!conversationContext) {
      return {
        cid,
        messages: [],
        childrenContexts: [],
      } as IConversationContextState;
    }
    const historyRelation = await this._historyService.getConversationRelation(cid);
    if (!historyRelation.ok) {
      return {
        cid,
        messages: conversationContext.messagesManager.getMessages(),
        childrenContexts: [],
      } as IConversationContextState;
    }
    const { childIds } = historyRelation.value as IConversationRelation;
    // 这里通过获取父子关系去装数据
    const { messages, parentId, parentMessageVersion } = conversationContext;
    // 获取子会话
    const children: IConversationContextState[] = [];
    if (childIds.length > 0) {
      const childCids = childIds.map((item) => item.cid);
      for (const childCid of childCids) {
        const childContext = await this._makeConversationContextChangeData(childCid);
        if (childContext) {
          children.push(childContext);
        }
      }
    }
    return {
      cid,
      parentCid: parentId,
      parentMessageVersion,
      messages,
      childrenContexts: children,
    };
  }

  private _getConversationData(options: {
    parentConversationId: string;
    parentMessageVersion: Int64;
  }): CreateConversationRequest {
    const { parentConversationId, parentMessageVersion } = options;
    // @ts-ignore
    const data: CreateConversationRequest = {
      type: ConversationType.Understanding,
    };
    if (parentConversationId && parentMessageVersion) {
      data.parent_id = parentConversationId;
      data.parent_version = parentMessageVersion;
    }
    return data;
  }

  private async _updateConversationRelation(options: {
    cid: string;
    parentConversationId: string;
    parentMessageVersion: Int64;
  }) {
    const { cid, parentConversationId, parentMessageVersion } = options;
    if (parentConversationId && parentMessageVersion) {
      try {
        await this._historyService.saveConversationRelation(options);
        console.debug(`[${this._conversationType}] 保存会话关系成功: ${parentConversationId}, ${cid}`);
      } catch (error) {
        console.error(`[${this._conversationType}] 保存会话关系失败:`, error);
      }
    }
  }
}
