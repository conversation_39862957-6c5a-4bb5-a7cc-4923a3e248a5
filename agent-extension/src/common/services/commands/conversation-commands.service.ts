import { lvAssertNotHere } from '@byted-image/lv-bedrock/assert';
import { type ILvErrorOr, makeErrorBy, makeOkWith } from '@byted-image/lv-bedrock/error';
import { window, ThemeIcon, Disposable, env } from 'vscode';
import {
  ConversationTypeEnum,
  DEFAULT_CONVERSATION_TYPE,
  getConversationFullLabel,
} from '../../constants/conversation-types';
import {
  IConversationHistoryService,
  type ConversationSummary,
} from '../conversation/history/conversation-history.interface';
import type { IConversationCommandsService } from './conversation-commands.interface';
import { IPrdChatService } from '../prd-chat/chat-service.interface';
import { ICodingChatService } from '../coding-chat/coding-chat-service.interface';
import { ID2cChatService } from '../d2c-chat/chat-service.interface';
import { AgentId } from '../conversation/const';
import { IRpcService } from '../rpc/rpc-service.interface';
import { IUnderstandingChatService } from '../understanding-chat/understanding-service.interface';

export class ConversationCommandsService implements IConversationCommandsService {
  public _serviceBrand: undefined;

  constructor(
    @IPrdChatService private readonly _prdChatService: IPrdChatService,
    @IUnderstandingChatService private readonly _understandingChatService: IUnderstandingChatService,
    @ICodingChatService private readonly _codingChatService: ICodingChatService,
    @ID2cChatService private readonly _d2cChatService: ID2cChatService,
    @IRpcService private readonly _rpcService: IRpcService,
    @IConversationHistoryService private readonly _historyService: IConversationHistoryService,
  ) {}

  private _getChatServiceByType(
    conversationType: ConversationTypeEnum,
  ): ICodingChatService | ID2cChatService | IPrdChatService | IUnderstandingChatService {
    switch (conversationType) {
      case ConversationTypeEnum.Coding:
        return this._codingChatService;
      case ConversationTypeEnum.D2c:
        return this._d2cChatService;
      case ConversationTypeEnum.TechDocs:
        return this._prdChatService;
      case ConversationTypeEnum.Understanding:
        return this._understandingChatService;
      default:
        lvAssertNotHere(`[getChatServiceByType] invalid conversationType: ${conversationType}`);
    }
  }

  /**
   * 根据会话类型获取标签
   */
  private _getConversationLabel(conversationType: ConversationTypeEnum): string {
    return getConversationFullLabel(conversationType);
  }

  async newConversation(conversationType?: ConversationTypeEnum): Promise<ILvErrorOr<string>> {
    try {
      const selectedType = conversationType;

      // 根据会话类型选择对应的服务并创建会话
      const newConversationType = selectedType || DEFAULT_CONVERSATION_TYPE;
      // const conversationService = this._getConversationServiceByType(newConversationType);
      const chatService = this._getChatServiceByType(newConversationType);
      // @ts-ignore
      const result = await chatService.switchCurrentConversation();
      const [error, cid] = result.pair();

      if (error) {
        throw new Error(error.msg);
      }

      // 通知webview更新
      this._rpcService.notify('newConversation', {
        conversationId: cid,
        conversationType: newConversationType,
        conversationLabel: this._getConversationLabel(newConversationType),
      });

      return makeOkWith(cid!);
    } catch (error) {
      window.showErrorMessage(`创建会话时发生错误: ${error}`);
      return makeErrorBy(-1, '创建会话时发生错误', new Error('创建会话时发生错误'));
    }
  }

  async viewHistory(): Promise<ILvErrorOr<void>> {
    try {
      const quickPick = window.createQuickPick();
      quickPick.placeholder = '选择要打开的会话';
      quickPick.matchOnDescription = true;
      quickPick.matchOnDetail = true;

      const disposables: Disposable[] = [];

      const loadHistoryItems = async () => {
        quickPick.busy = true;
        quickPick.show();

        const result = await this._historyService.fetchConversations(undefined, 1000);
        const [error, data] = result.pair();

        if (error) {
          window.showErrorMessage(`获取会话历史失败: ${error}`);
          quickPick.hide();
          return;
        }

        const allConversations = data?.conversations || [];
        if (!allConversations || allConversations.length === 0) {
          window.showInformationMessage('暂无历史会话');
          quickPick.hide();
          return;
        }

        allConversations.sort((a, b) => new Date(b.lastUpdated).getTime() - new Date(a.lastUpdated).getTime());

        quickPick.items = this._filterConversationHistory(allConversations).map((conv: ConversationSummary) => {
          const conversationType =
            conv.conversationType ||
            (() => {
              if (conv.agentType === AgentId.Coding) return ConversationTypeEnum.Coding;
              if (conv.agentType === AgentId.D2c) return ConversationTypeEnum.D2c;
              return ConversationTypeEnum.Coding;
            })();

          const typeLabel = this._getConversationLabel(conversationType);

          return {
            label: conv.title,
            description: `${conv.messageCount} 条消息 | ${typeLabel}`,
            detail: `ID: ${conv.id} | 最后更新: ${new Date(conv.lastUpdated).toLocaleString()}`,
            id: conv.id, // Custom property to hold id
            conversationType: conversationType, // Custom property
            buttons: [
              {
                iconPath: new ThemeIcon('copy'),
                tooltip: '复制会话ID',
              },
              {
                iconPath: new ThemeIcon('trash'),
                tooltip: '删除此条会话',
              },
            ],
          };
        });
        quickPick.busy = false;
      };

      disposables.push(
        quickPick.onDidAccept(async () => {
          const selected = quickPick.selectedItems[0] as any;
          if (selected) {
            const service = this._getChatServiceByType(selected.conversationType);
            const switchResult = await service.switchCurrentConversation(selected.id);
            const [switchError] = switchResult.pair();

            if (switchError) {
              window.showErrorMessage(`切换会话失败: ${switchError}`);
              return;
            }

            const messages = service.getMessages();
            const serializedMessages = messages.map((message) => message.toJSON());

            this._rpcService.notify('switchConversation', {
              conversationId: selected.id,
              conversationType: selected.conversationType,
              conversationLabel: this._getConversationLabel(selected.conversationType),
              messages: serializedMessages,
              contextState: await service.getCurrentContextState(),
            });

            this._rpcService.notify('state', {
              page: selected.conversationType,
              payload: JSON.stringify({
                messages: serializedMessages,
              }),
            });
          }
          quickPick.hide();
        }),

        quickPick.onDidTriggerItemButton(async (e) => {
          const item = e.item as any;
          const buttonIndex = e.button === item.buttons[0] ? 0 : 1;

          if (buttonIndex === 0) {
            // 复制会话ID
            await env.clipboard.writeText(item.id);
            window.showInformationMessage(`会话ID已复制到剪贴板: ${item.id}`);
          } else if (buttonIndex === 1) {
            // 删除会话
            const confirmation = await window.showInformationMessage(
              `您确定要删除会话 "${item.label}" 吗？此操作无法撤销。`,
              { modal: true },
              '确认删除',
            );

            if (confirmation === '确认删除') {
              await this._historyService.deleteConversation(item.id);
              window.showInformationMessage('会话已删除。');
              // 删除后直接关闭 quickPick，不再刷新列表
              quickPick.hide();
            }
          }
        }),
        quickPick.onDidHide(() => {
          for (const d of disposables) {
            d.dispose();
          }
        }),
      );

      await loadHistoryItems();

      return makeOkWith(undefined);
    } catch (error) {
      window.showErrorMessage(`查看历史会话时发生错误: ${error}`);
      return makeOkWith(undefined);
    }
  }

  /** 因为只展示了coding，还原历史会话时隐藏其他类型 */
  private _filterConversationHistory(allConversations: ConversationSummary[]) {
    const validAgentTypes = IS_DEV ? [AgentId.Coding, AgentId.Private] : [AgentId.Coding];
    return allConversations.filter((conversationSummary) => validAgentTypes.includes(conversationSummary.agentType));
  }
}
