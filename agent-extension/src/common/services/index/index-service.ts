import { IInstantiationService } from '@byted-image/lv-bedrock/di';
import type { IIndexService } from './index-service.interface';
import type { MerkleHolderSyncBuildIndexEvent } from '../merkle/merkle-holder';
import { makeOkWith, type ILvErrorOr } from '@byted-image/lv-bedrock/error';
import { window } from 'vscode';
import { IMerkleService } from '../merkle/merkle-service.interface';

export class IndexService implements IIndexService {
  _serviceBrand: undefined;
  _merkleService: IMerkleService;

  constructor(@IInstantiationService protected readonly _instantiationService: IInstantiationService) {
    this._merkleService = this._instantiationService?.invokeFunction((accessor) => {
      return accessor.get(IMerkleService);
    });
  }

  viewHistory(): ILvErrorOr<void> {
    try {
      const quickPick = window.createQuickPick();
      quickPick.placeholder = '查看索引构建历史';
      quickPick.matchOnDescription = true;
      quickPick.matchOnDetail = true;

      const loadHistoryItems = () => {
        quickPick.busy = true;
        quickPick.show();

        const allRecords = this._merkleService.buildRecords || [];
        if (!allRecords || allRecords.length === 0) {
          window.showInformationMessage('暂无构建历史');
          quickPick.hide();
          return;
        }

        allRecords.sort((a, b) => new Date(b.lastUpdated).getTime() - new Date(a.lastUpdated).getTime());

        quickPick.items = allRecords.map((conv: MerkleHolderSyncBuildIndexEvent) => {
          return {
            label: `类型：${conv.label}   /${conv.repoInfo.repoName}/${conv.repoInfo.branch}`,
            description: `merkId: ${conv.rootHash}`,
            detail: `最后更新: ${new Date(conv.lastUpdated).toLocaleString()} 状态: ${conv.status}`,
            id: conv.rootHash,
          };
        });
        quickPick.busy = false;
      };

      loadHistoryItems();

      return makeOkWith(undefined);
    } catch (error) {
      window.showErrorMessage(`查看索引构建历史时发生错误: ${error}`);
      return makeOkWith(undefined);
    }
  }
}
