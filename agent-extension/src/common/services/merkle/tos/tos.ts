import { TosClient as Tos } from '@volcengine/tos-sdk';

export interface TosClientOptions {
  bucket: string;
  ak: string;
  sk: string;
  endpoint: string;
  region: string;
}

class TosClient {
  private _client: Tos;

  constructor(options: TosClientOptions) {
    this._client = new Tos({
      accessKeyId: options.ak,
      accessKeySecret: options.sk,
      region: options.region,
      endpoint: options.endpoint,
      bucket: options.bucket,
      requestTimeout: -1,
    });
  }

  async upload(key: string, file: Buffer): Promise<boolean> {
    try {
      const ret = await this._client.putObject({
        key,
        body: file,
        // forbidOverwrite: true,
        progress: (progress) => {
          console.log(progress);
        },
      });
      return ret.statusCode === 200;
    } catch (ex) {
      console.error(ex);
      return false;
    }
  }

  async batchUpload(files: Record<string, Buffer>) {
    const errorKeys: string[] = [];
    const promises = Object.entries(files).map(async ([key, file]) => {
      const ret = await this.upload(key, file);
      if (!ret) {
        errorKeys.push(key);
      }
    });
    await Promise.all(promises);

    return {
      status: errorKeys.length === 0,
      errorKeys,
    };
  }
}

export type ITosClient = TosClient;

const bucketMap = new Map<string, TosClient>();

export function getTosClient(options: TosClientOptions) {
  const { bucket } = options;
  let tos = bucketMap.get(bucket);
  if (!tos) {
    tos = new TosClient(options);
    bucketMap.set(bucket, tos);
  }
  return tos;
}
