import { sleep } from '@byted-image/lv-bedrock/async';
import type { BaseRequest } from './api/base';
import { makeDiffRequest } from './api/vector-merkle-diff';
import { makeQueryRequest } from './api/vector-build-query';
import { makeUploadRequest, type UploadRequest } from './api/vector-merkle-upload';
import { getChunkFile } from './chunk/chunk-file';
import { getFileContentMap } from './chunk/get-content-map';
import { compress } from './compress/compress';
import { SyncEventName, type SyncEventParams } from './slardar/event-type';
import { PerfEvent } from './slardar/perf-event';
import type { DiffType } from './type';
import { MerkleHolder, type MerkleHolderOptions } from './merkle-holder';
import { isIgnoredByMap, updateMerkleTree } from '@byted-image/merkle';
import { getTosClient, type ITosClient } from './tos/tos';
// import { writeFileSync } from 'node:fs';

/**
 * MerkleHolder 类负责为每个 Git 仓库管理和维护 Merkle 树。
 * 它监听仓库的文件变化和分支切换事件，根据变化更新 Merkle 树，并与服务端同步数据。
 * 由于一个 workspace 可能包括多个 repo，所以每个 repo 都有一个 MerkleHolder
 */
export class VectorMerkleHolder extends MerkleHolder {
  label = 'vector';

  private _tosClient: ITosClient;

  /**
   * 构造函数，初始化 MerkleHolder 实例。
   * @param options - 初始化所需的配置选项，包括用户 ID、设备 ID、Git 仓库实例和日志记录器。
   */
  constructor(options: MerkleHolderOptions) {
    super(options, 5 * 60 * 1000);
    // @todo ryan from tcc or something
    this._tosClient = getTosClient({
      ak: 'AKLTMmM0NzE4MTliZjc3NGU3NmIxNTJjYjIyOGI1ZTBhMWE',
      sk: 'TlRNd1pEVXdOMlEwWTJGaE5HWXpNMkkzTkRJNVlUQTVNMkpoTVdWaE9UUQ==',
      endpoint: 'tos-cn-beijing.volces.com',
      region: 'cn-beijing',
      bucket: 'merklestorage',
    });
  }

  /**
   * 将 Merkle 树和差异文件块同步到服务端。
   * 内部也会更新 tree
   */
  protected async _doSync(): Promise<boolean> {
    if (!this._tree) {
      this._logger.error('[_doSync] tree is not initialized');
      return false;
    }

    const treeHash = this._tree.hash;

    const syncEvent = new PerfEvent<SyncEventParams>(this._logger, SyncEventName);
    let buildStatus = true;
    const baseRequest: BaseRequest = {
      uid: this._uid,
      did: this._did,
      branch: this.branch,
      repo_name: this.dbName,
      repo_path: this.root,
    };

    this._logger.log(`[_doSync] sync hash=${treeHash} baseRequest=${JSON.stringify(baseRequest)}`);

    // 1. diff (基于前端上传的树，远端树应该产生的差异)
    syncEvent.stageStart('diff');
    const serializedTree = await this.serializeTree();
    const compressedTree = await compress(serializedTree);

    // 1.1 upload to tos
    const treeFileKey = treeHash; // 这里隐性依赖了服务端的文件命名规则，服务端下载的时候直接用的 hash
    syncEvent.stageStart('pre_diff');
    const preDiffStatus = await this._tosClient.upload(treeFileKey, Buffer.from(compressedTree));
    syncEvent.stageEnd('pre_diff');
    if (!preDiffStatus) {
      syncEvent.emit({
        status: 'fail',
        reason: 'pre diff upload fail',
      });
      return false;
    }
    // writeFileSync(path.join(this.root, 'tree1.json'), serializedTree);
    const diffResp = await makeDiffRequest({
      ...baseRequest,
      merkle_tree_key: treeFileKey,
    });
    syncEvent.stageEnd('diff', {
      diffLogId: diffResp.__logid,
    });

    diffResp.diffs = diffResp.diffs.filter((item) => !isIgnoredByMap(item.path, this._ignoreMap, this._regexMap));
    this._logger.log(
      `[_doSync] sync tree diff found ${diffResp.diffs.length} changes, origin_id=${diffResp.origin_user_knowledge_id}, logid=${diffResp.__logid}`,
      diffResp.diffs,
    );

    if (diffResp.diffs.length === 0) {
      // 没有diff的话，说明远端和本地一致，或者远端还没有构建索引
      // 如果没有变化，并且有root_merkle_id，说明远端已经构建了索引，直接返回
      // 存在一种情况是用户从master创建新的分支，此时diff为0，且没有rootMerkleId
      // 这个时候需要提前触发用户db索引的copyFrom，方便后续用户可能直接进行语义搜索，搜到的是新的userKnowledgeId
      const queryRespInitial = await makeQueryRequest({ ...baseRequest });
      if (queryRespInitial.root_merkle_id !== '') {
        syncEvent.emit({
          status: 'skip',
          reason: 'no diff',
        });
        return buildStatus;
      }
    }

    let newRootHash = this._tree!.hash;
    let compressedData: { chunk: ArrayBuffer | null; relations: ArrayBuffer | null } = {
      chunk: null,
      relations: null,
    };
    let compressedTree2: ArrayBuffer = compressedTree;

    const nonDeleteDiffs: Map<string, DiffType> = new Map();
    for (const diff of diffResp.diffs) {
      // 如果是 delete，表示后端应该删除，客户端只需要在后面的 upload 接口里透传这个
      // 只有 add 和 modify，才需要前端将代码上行给后端
      if (diff.type !== 'delete') {
        nonDeleteDiffs.set(diff.path, diff.type);
      }
    }

    if (nonDeleteDiffs.size) {
      // 2. get files' content and save to memory
      const filePaths = Array.from(nonDeleteDiffs.keys());
      const contentMap = await getFileContentMap(this.root, filePaths, this._createLimitFn());

      syncEvent.update({
        diffCount: diffResp.diffs.length,
        chunkCount: filePaths.length,
      });

      // merkle tree应该以前端为准，前端不需要更新，前端覆盖后端
      // 3.a update merkle tree
      syncEvent.stageStart('tree_update');
      await updateMerkleTree(baseRequest.repo_path, this._tree!, nonDeleteDiffs, contentMap);
      newRootHash = this._tree!.hash;
      const serializedTree2 = await this.serializeTree();
      compressedTree2 = await compress(serializedTree2);
      syncEvent.stageEnd('tree_update', {
        treeSize: compressedTree2.byteLength,
      });
      // writeFileSync(path.join(this.root, 'tree2.json'), serializedTree);

      // 3.b chunk files
      syncEvent.stageStart('chunk');
      compressedData = await getChunkFile(baseRequest.repo_name, baseRequest.repo_path, contentMap);
      syncEvent.stageEnd('chunk', {
        chunkSize: (compressedData.chunk as unknown as ArrayBuffer)?.byteLength ?? 0,
        relationSize: (compressedData.relations as unknown as ArrayBuffer)?.byteLength ?? 0,
      });

      if (!compressedData.chunk) {
        syncEvent.emit({
          status: 'skip',
          reason: 'no chunk file',
        });
        return buildStatus;
      }
    }

    // 4. check remote db status
    const queryResp = await makeQueryRequest({ ...baseRequest });
    if (queryResp.build_status === '-1') {
      syncEvent.emit({
        status: 'skip',
        reason: 'remote db not exist',
      });
      return buildStatus;
    }
    if (queryResp.root_merkle_id && queryResp.build_status === '0') {
      syncEvent.emit({
        status: 'skip',
        reason: 'remote db is updating',
      });
      return buildStatus;
    }

    // 5. upload to tos & create request obj
    const newTreeFileKey = newRootHash; // 这里隐性依赖了服务端的文件命名规则，服务端下载的时候直接用的 hash;
    const uploadRequest: UploadRequest = {
      ...baseRequest,
      merkle_tree_key: newTreeFileKey,
      root_merkle_id: newRootHash,
      origin_user_knowledge_id: diffResp.origin_user_knowledge_id,
      delete_file_ids: diffResp.diffs
        .filter((item) => item.type !== 'add')
        .map((item) => item.hash)
        .join(','), // modify 在远端是删除后新增的逻辑，所以也算删除
    };
    const uploadFiles: Record<string, Buffer> = {
      [newTreeFileKey]: Buffer.from(compressedTree2),
    };
    if (compressedData.chunk) {
      const chunkFileKey = `${newRootHash}_chunk.gz`;
      uploadFiles[chunkFileKey] = Buffer.from(compressedData.chunk);
      uploadRequest.chunk_file_key = chunkFileKey;
    }
    if (compressedData.relations) {
      const relationsFileKey = `${newRootHash}_relations.gz`;
      uploadFiles[relationsFileKey] = Buffer.from(compressedData.relations);
      uploadRequest.relations_file_key = relationsFileKey;
    }

    syncEvent.stageStart('pre_upload');
    const { status: preUploadStatus, errorKeys } = await this._tosClient.batchUpload(uploadFiles);
    syncEvent.stageEnd('pre_upload');
    if (!preUploadStatus) {
      syncEvent.emit({
        status: 'fail',
        reason: `pre_upload failed, keys: ${errorKeys}`,
      });
      return false;
    }

    // 6. call server api to build index
    syncEvent.stageStart('upload');
    const uploadResp = await makeUploadRequest(uploadRequest);
    syncEvent.stageEnd('upload', {
      uploadLogId: uploadResp.__logid,
      rootHash: newRootHash,
    });
    if (!uploadResp.id) {
      if (!uploadResp.id && !diffResp.origin_user_knowledge_id) {
        syncEvent.emit({
          status: 'fail',
          reason: '已知问题，因为早期索引构建的id存在脏数据，暂时忽略，后续会重建',
        });
        return false;
      }
      syncEvent.emit({
        status: 'fail',
        reason: 'upload failed',
      });
      return false;
    }

    // 7. loop query until it's built
    syncEvent.stageStart('query_upload');
    let done = false;
    let success = false;
    let queryRetryTimes = 3;
    while (!done) {
      await sleep(10_000);
      try {
        const queryResp = await makeQueryRequest({ ...baseRequest });
        this._logger.log('[_doSync] query update resp', queryResp);
        if (queryResp.root_merkle_id !== newRootHash) {
          syncEvent.emit({
            status: 'fail',
            reason: 'root hash mismatch', // 也可能在 10s 内同步完成了并且外部触发了一次新的更新
          });
          return false;
        }
        if (queryResp.build_status !== '0') {
          done = true;
          if (queryResp.build_status === '1') {
            success = true;
          }
        }
      } catch (ex) {
        if (queryRetryTimes <= 0) {
          this._logger.error(ex);
          done = true;
        }
        // query error can not fail the sync
        queryRetryTimes--;
      }
    }
    syncEvent.stageEnd('query_upload');
    if (success) {
      // 外部感知
      buildStatus = true;
      syncEvent.emit({
        status: 'success',
      });
      this._lastSyncHash = newRootHash;
    } else {
      // 外部感知
      buildStatus = false;
      // 内部上报
      syncEvent.emit({
        status: 'fail',
        reason: 'query upload failed',
      });
    }
    return buildStatus;
  }
}
