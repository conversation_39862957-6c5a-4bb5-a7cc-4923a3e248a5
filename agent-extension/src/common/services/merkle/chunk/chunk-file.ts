import { incrementalUpdate as chunk } from '@byted-image/codin-indexer';
import { compress } from '../compress/compress';
import type { IFileContentMap } from './get-content-map';

export async function getChunkFile(
  repoName: string,
  rootPath: string,
  contentMap: IFileContentMap,
): Promise<{ chunk: ArrayBuffer | null; relations: ArrayBuffer | null }> {
  const chunkResp = await chunk(
    rootPath,
    contentMap,
    repoName,
    'typescript', // @fixme by repo language
  );
  if (!chunkResp || chunkResp.chunks.length === 0) {
    return { chunk: null, relations: null };
  }
  const { chunks, relations } = chunkResp;

  const compressedChunk = await compress(JSON.stringify(chunks));
  const compressedRelations = await compress(JSON.stringify(relations));
  return {
    chunk: compressedChunk,
    relations: compressedRelations,
  };
}
