import vscode from 'vscode';
import { IInstantiationService } from '@byted-image/lv-bedrock/di';
import { Disposable } from '@byted-image/lv-bedrock/dispose';
import { IAccountService } from '../account/account-service.interface';
import { IGitService } from '../git/git-service.interface';
import { MerkleHolder, type MerkleHolderSyncBuildIndexEvent } from './merkle-holder';
import { IMerkleService } from './merkle-service.interface';
import { getRepoRemoteURL } from './git/get-repo-remote-url';
import { OutputLogger } from '@/utils/output-logger';
import type { Repository } from '@/typings/git';
import { getRepoBranch } from './git/get-repo-branch';
import { initSlardarLogger, slardarLogger } from './slardar/slardar';
import { machineIdSync } from '@/common/utils/machine';
import { IWorkspaceService } from '../workspace/workspace.interface';
import { extractRepoPath } from '../workspace/util';
import { VectorMerkleHolder } from './vector-merkle-holder';
// import { SummaryMerkleHolder } from './summary-merkle-holder';
import { getRepoConfig, type RepoConfigItem } from './api/repo-config';
import { SummaryMerkleHolder } from './summary-merkle-holder';
import { IRepoInfo } from '../workspace/workspace.service';

export const MERKLE_SERVICE = 'merkle-service';

/**
 * MerkleService 类负责管理和维护 Merkle 树，监听 Git 仓库的变化，并在必要时更新 Merkle 树。
 * 它与 Git 服务和账户服务集成，确保每个仓库都有对应的 Merkle 树持有者。
 */
export class MerkleService extends Disposable implements IMerkleService {
  public readonly _serviceBrand: undefined;

  private _did: string;

  private _vectorLogChannel: vscode.LogOutputChannel;
  private _summaryLogChannel: vscode.LogOutputChannel;
  private _vectorLogger: OutputLogger;
  private _summaryLogger: OutputLogger;
  private _watcherDisposableMap: Map<string, vscode.Disposable> = new Map();

  private _accountService: IAccountService;
  private _gitService: IGitService;
  private _workspaceService: IWorkspaceService;

  private _repoMerkleHolderMap: Map<string, Array<MerkleHolder>> = new Map();
  private _buildRecords: MerkleHolderSyncBuildIndexEvent[] = [];
  private _repoConfig: RepoConfigItem[] = [];
  /**
   * 构造函数，初始化 MerkleService 实例。
   * @param _instantiationService - 用于实例化依赖服务的服务。
   */
  constructor(@IInstantiationService protected readonly _instantiationService: IInstantiationService) {
    super();
    this._vectorLogChannel = vscode.window.createOutputChannel('Codin: Vector Merkle Tree', {
      log: true,
    });
    this._summaryLogChannel = vscode.window.createOutputChannel('Codin: Summary Merkle Tree', {
      log: true,
    });
    this._vectorLogger = new OutputLogger(this._vectorLogChannel, '', `${MERKLE_SERVICE}_vector`);
    this._summaryLogger = new OutputLogger(this._summaryLogChannel, '', `${MERKLE_SERVICE}_summary`);

    this._did = machineIdSync();
    this._accountService = this._instantiationService.invokeFunction((accessor) => accessor.get(IAccountService));
    this._gitService = this._instantiationService.invokeFunction((accessor) => accessor.get(IGitService));
    this._workspaceService = this._instantiationService.invokeFunction((accessor) => accessor.get(IWorkspaceService));
    this._initialize();
  }

  private get _uid() {
    return this._accountService.getUserInfo()?.userId ?? 'guest';
  }

  public get buildRecords() {
    return this._buildRecords;
  }
  /**
   * 初始化 Merkle 服务，记录日志并初始化仓库的 Merkle 树持有者映射。
   */
  private async _initialize(): Promise<void> {
    await Promise.all([
      getRepoConfig().then((config) => {
        this._repoConfig = config;
      }),
      this._accountService.initialize(),
    ]);

    initSlardarLogger();
    slardarLogger.config({
      deviceId: this._did,
      userId: this._uid,
    });
    this._initHolderMap();
  }

  /**
   * 初始化每个 Git 仓库的 Merkle 树持有者映射。
   * 监听新仓库的打开事件，并为现有的仓库初始化 Merkle 树持有者。
   */
  private _initHolderMap() {
    const gitApi = this._gitService.getGitApi();
    if (!gitApi) {
      return;
    }

    for (const repo of gitApi.repositories) {
      this._initHolder(repo, 'init');
    }
    gitApi.onDidOpenRepository((repo) => {
      this._initHolder(repo, 'onDidOpenRepository');
    });
    gitApi.onDidCloseRepository(this._removeHoldersByRepo.bind(this));
  }

  private _removeHoldersByRepo(repo: Repository) {
    const repoName = extractRepoPath(repo.state.remotes[0].fetchUrl ?? '');
    if (!repoName) {
      return;
    }
    this._vectorLogger.log('[_removeHoldersByRepo] repoName:', repoName);
    this._watcherDisposableMap.get(repoName)?.dispose();
    this._watcherDisposableMap.delete(repoName);
    this._repoMerkleHolderMap.get(repoName)?.forEach((holder) => holder.dispose());
    this._repoMerkleHolderMap.delete(repoName);
  }

  /**
   * 为指定的 Git 仓库初始化 Merkle 树持有者，并将其添加到映射中。
   * @param repo - 要初始化的 Git 仓库。
   */
  private _initHolder(repo: Repository, scene: string) {
    this._vectorLogger.log(`[_initHolder] start for ${repo.rootUri.fsPath} in scene=${scene}`);

    if (!repo.state.remotes || repo.state.remotes.length === 0) {
      this._vectorLogger.log(
        `[_initHolder] repo remote url is empty for ${repo.rootUri.fsPath}, will watch repo change`,
      );
      const repoWatcher = repo.state.onDidChange(() => {
        this._initHolder(repo, 'no-remote');
        repoWatcher.dispose();
      });
      return;
    }

    const repoRemoteURL = getRepoRemoteURL(repo);
    if (!repoRemoteURL) {
      this._vectorLogger.log(`[_initHolder] repo remote url is still empty for ${repo.rootUri.fsPath}`);
      return;
    }
    const repoName = extractRepoPath(repo.state.remotes[0].fetchUrl ?? '');
    const repoConfig = this._repoConfig.find((item) => item.git_repo_name === repoName);
    if (!repoConfig) {
      this._vectorLogger.log(`[_initHolder] no repo config found for ${repo.rootUri.fsPath}, will skip`);
      return;
    }

    const repoInfo = structuredClone(this._workspaceService.getRepoInfo(repoName)); // 这里的返回是 mutable 的
    if (!repoInfo) {
      this._vectorLogger.log(`[_initHolder] no repo info found for ${repo.rootUri.fsPath}`);
      return;
    }

    const existHolder = this._repoMerkleHolderMap.get(repoInfo.repoName);
    if (existHolder) {
      this._vectorLogger.log(`[_initHolder] holder already exist for ${repo.rootUri.fsPath}, dispose`);
      this._removeHoldersByRepo(repo);
    }

    const vectorMerkleHolder = new VectorMerkleHolder({
      uid: this._accountService.getUserInfo()?.userId ?? 'guest',
      did: this._did,
      repo: repo,
      logger: this._vectorLogger.derive(`[Holder#${repoInfo.repoName}]`),
      repoInfo: repoInfo,
    });

    const summaryMerkleHolder = new SummaryMerkleHolder({
      uid: this._uid,
      did: this._did,
      repo: repo,
      logger: this._summaryLogger.derive(`[Holder#${repoInfo.repoName}]`),
      repoInfo,
      hashConcurrency: 100,
      hashInterval: 20,
    });
    this._repoMerkleHolderMap.set(repoInfo.repoName, [summaryMerkleHolder, vectorMerkleHolder]);

    const repoWatcher = repo.state.onDidChange(() => {
      const newBranch = getRepoBranch(repo);
      if (newBranch === repoInfo.branch) {
        // no branch change, skip
        return;
      }

      this._vectorLogger.log(
        `[_initHolder] repo branch changed for ${repoInfo.repoName}: ${repoInfo.branch} => ${newBranch}`,
      );
      this._initHolder(repo, 'branch-change');
    });
    this._watcherDisposableMap.set(repoInfo.repoName, repoWatcher);

    vectorMerkleHolder.onBuildIndex((event) => {
      this._buildRecords.push(event);
    });
    summaryMerkleHolder.onBuildIndex((event) => {
      this._buildRecords.push(event);
    });
  }

  /**
   * 释放资源，关闭日志通道并释放所有 Merkle 树持有者的资源。
   */
  public dispose() {
    this._vectorLogChannel.dispose();
    this._summaryLogChannel.dispose();
    for (const merkleHolder of this._repoMerkleHolderMap.values()) {
      merkleHolder.forEach((holder) => holder.dispose());
    }
    this._repoMerkleHolderMap.clear();
  }
}
