import { slardarLogger } from '../slardar/slardar';

export const apiDomain = 'https://capcut-devops.byted.org';
// uncomment to debug, change the env to your custom env
export const commonHeaders = {
  'content-type': 'application/json',
  // 'x-tt-trace': '1',
  // 'x-tt-env': 'ppe_codin_ryan',
  // 'x-use-ppe': '1',
};

export interface BaseRequest {
  // 仓库名称
  repo_name: string;
  // 分支
  branch: string;
  // 用户id
  uid: string;
  // 仓库路径
  repo_path: string;
  // 设备id
  did: string;
}

export interface BaseResponse {
  __logid: string;
}

export function obj2FormData(obj: Record<string, string | [Blob, string] | undefined>) {
  const formData = new FormData();
  for (const key in obj) {
    const val = obj[key];
    if (val === undefined) {
      continue;
    }
    if (Array.isArray(val) && val.length === 2 && val[0] instanceof Blob) {
      formData.append(key, val[0], val[1]);
    } else {
      formData.append(key, val as string);
    }
  }
  return formData;
}

export function pickReportCtx(req: Record<string, any>) {
  const ctx: Record<string, string> = {};
  for (const key in req) {
    const val = req[key];
    if (typeof val === 'string') {
      ctx[key] = val;
    }
    if (typeof val === 'number') {
      ctx[key] = val.toString();
    }
  }
  return ctx;
}

export async function handleResponse(api: string, resp: Response, reportCtx: Record<string, any> = {}) {
  let apiPath = api;
  try {
    const urlObj = new URL(api);
    apiPath = urlObj.pathname;
  } catch (ex) {
    // ignore
  }

  const logid = resp.headers.get('x-tt-logid') ?? 'unknown';
  if (!resp.ok) {
    let errmsg = 'unknown errmsg';
    try {
      errmsg = await resp.text();
    } catch (ex) {
      errmsg = `unknown errmsg: ${ex}`;
    }
    slardarLogger.error(`${api} response not ok`, {
      api,
      logid,
      status: resp.status.toString(),
      errmsg,
      ...pickReportCtx(reportCtx),
    });
    throw new Error(`[handleResponse#${apiPath}] ${resp.status} (${resp.statusText}) msg=${errmsg} logid=${logid}`);
  }

  try {
    const data = await resp.json();
    data.__logid = logid;
    return data;
  } catch (ex) {
    slardarLogger.error(ex, {
      api,
      logid,
      ...reportCtx,
    });
    throw new Error(`[handleResponse#${apiPath}] parse response json error logid=${logid}`);
  }
}
