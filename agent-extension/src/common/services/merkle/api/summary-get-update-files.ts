import type { DiffType } from '../type';
import { apiDomain, commonHeaders, handleResponse, obj2FormData, type BaseRequest, type BaseResponse } from './base';

interface SummaryGetUpdateFilesRequest extends BaseRequest {
  repo_tree: [Blob, string];
}

export interface GroupedRelatedPathInfo {
  module_groups: DirPathGroup[];
  leaf_groups: DirPathGroup[];
}

interface DirPathGroup {
  group_path: string;
  // todo(liboti) 确认一下这里有没有作用
  sub_dir_paths: string[];
}

interface SummaryGetUpdateFilesResponse extends BaseResponse {
  grouped_related_path_info: GroupedRelatedPathInfo;
  diffs: {
    path: string;
    type: DiffType;
    hash: string;
  }[];
  result: string;
  code: string;
  origin_user_knowledge_id: string;
}

// 上行merkle tree，后台根据merkle tree对比差异，返回需要重新上行的模块信息给后端
// 上行的模块信息是变更文件内容查找上下游module，然后返回的一个最小的完整集合，方便后端重新做module的summary
export async function makeSummaryGetUpdateFilesRequest(
  summaryGetUpdateFilesRequest: SummaryGetUpdateFilesRequest,
): Promise<SummaryGetUpdateFilesResponse> {
  const api = `${apiDomain}/summary/get-update-files`;
  const formData = obj2FormData(
    summaryGetUpdateFilesRequest as unknown as Record<string, string | [Blob, string] | undefined>,
  );

  const resp = await fetch(api, {
    method: 'POST',
    headers: commonHeaders,
    body: formData,
  });

  return handleResponse(api, resp, summaryGetUpdateFilesRequest);
}
