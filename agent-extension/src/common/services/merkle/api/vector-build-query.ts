import { apiDomain, commonHeaders, handleResponse, type BaseRequest, type BaseResponse } from './base';

interface QueryRequest extends BaseRequest, Record<string, string> {
  //
}

export interface QueryResponse extends BaseResponse {
  root_merkle_id: string; // 为空表示云端无构建
  build_status: '0' | '1' | '2' | '-1'; // 0: 构建中 1: 已构建 2: 构建失败, -1: 索引不存在
}

export async function makeQueryRequest(queryRequest: QueryRequest): Promise<QueryResponse> {
  const api = `${apiDomain}/merklet/query-build`;

  const resp = await fetch(api, {
    method: 'POST',
    headers: {
      ...commonHeaders,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(queryRequest),
  });

  return handleResponse(api, resp, queryRequest);
}
