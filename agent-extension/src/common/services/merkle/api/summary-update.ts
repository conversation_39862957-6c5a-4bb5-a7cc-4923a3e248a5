import { apiDomain, commonHeaders, handleResponse, obj2FormData, type BaseRequest, type BaseResponse } from './base';

interface SummaryUpdateRequest extends BaseRequest {
  repo_tree: [Blob, string];
  origin_user_knowledge_id: string;
  root_merkle_id: string;
  grouped_related_file_info?: [Blob, string];
}

interface SummaryUpdateResponse extends BaseResponse {
  user_knowledge_id: string;
}

interface FileInfo {
  file_path: string;
  file_content: string;
}

export interface FileContentGroup {
  group_path: string;
  sub_file_infos: FileInfo[];
}

export interface GroupedRelatedFileInfo {
  module_groups: FileContentGroup[];
  leaf_groups: FileContentGroup[];
}

// 上行最终版的变更文件给后端，后端根据数据进行summary的更新
export async function makeSummaryUpdateRequest(
  summaryUpdateRequest: SummaryUpdateRequest,
): Promise<SummaryUpdateResponse> {
  const api = `${apiDomain}/summary/update`;
  const formData = obj2FormData(summaryUpdateRequest as unknown as Record<string, string | [Blob, string] | undefined>);

  const resp = await fetch(api, {
    method: 'POST',
    headers: commonHeaders,
    body: formData,
  });

  return handleResponse(api, resp, summaryUpdateRequest);
}
