import { apiDomain, commonHeaders, handleResponse, type BaseRequest, type BaseResponse } from './base';

export interface UploadRequest extends BaseRequest, Record<string, string | undefined> {
  chunk_file_key?: string;
  merkle_tree_key: string;
  relations_file_key?: string;
  delete_file_ids: string;
  origin_user_knowledge_id: string;
  root_merkle_id: string;
}

interface UploadResponse extends BaseResponse {
  id: string;
}

export async function makeUploadRequest(uploadRequest: UploadRequest): Promise<UploadResponse> {
  const api = `${apiDomain}/merklet/upload`;

  const resp = await fetch(api, {
    method: 'POST',
    headers: commonHeaders,
    body: JSON.stringify(uploadRequest),
  });

  return handleResponse(api, resp, uploadRequest);
}
