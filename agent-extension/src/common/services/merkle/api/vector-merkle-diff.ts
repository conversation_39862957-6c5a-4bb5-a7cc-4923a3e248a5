import type { DiffType } from '../type';
import { apiDomain, commonHeaders, handleResponse, type BaseRequest, type BaseResponse } from './base';

interface DiffRequest extends BaseRequest, Record<string, string | [Blob, string]> {
  merkle_tree_key: string;
}

interface DiffResponse extends BaseResponse {
  diffs: {
    path: string;
    type: DiffType;
    hash: string;
  }[];
  origin_user_knowledge_id: string;
}

export async function makeDiffRequest(diffRequest: DiffRequest): Promise<DiffResponse> {
  const api = `${apiDomain}/merklet/diff`;

  const resp = await fetch(api, {
    method: 'POST',
    headers: commonHeaders,
    body: JSON.stringify(diffRequest),
  });

  return handleResponse(api, resp, diffRequest);
}
