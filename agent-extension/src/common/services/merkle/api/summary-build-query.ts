import { apiDomain, commonHeaders, handleResponse, type BaseRequest, type BaseResponse } from './base';

interface SummaryBuildQueryRequest extends BaseRequest {
  //
}

export interface SummaryBuildQueryResponse extends BaseResponse {
  root_merkle_id: string; // 为空表示云端无构建
  // todo(liboti) 后续改枚举
  build_status: '0' | '1' | '2' | '-1'; // 0: 构建中 1: 已构建 2: 构建失败 -1: 无构建
  result: string; // 结果
  code: string; // 接口回包
}

// 查询构建状态
export async function makeSummaryBuildQueryRequest(
  queryRequest: SummaryBuildQueryRequest,
): Promise<SummaryBuildQueryResponse> {
  const api = `${apiDomain}/summary/query-build`;

  const resp = await fetch(api, {
    method: 'POST',
    headers: {
      ...commonHeaders,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(queryRequest),
  });

  return handleResponse(api, resp, queryRequest);
}
