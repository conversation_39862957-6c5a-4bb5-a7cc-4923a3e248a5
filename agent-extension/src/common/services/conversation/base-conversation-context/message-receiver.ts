import { ClientStreamingMessage } from '@/conversation/client-message/streaming-message';
import { ClientToolMessage } from '@/conversation/client-message/tool-message';
import type { ClientMessage } from '@/conversation/client-message/abstract-message';
import type { ErrorEvent, MessageEvent, SocketClient } from '@/common/services/socket/socket-service.interface';
import type { IDisposable } from '@byted-image/lv-bedrock/dispose';
import { PushType, type Message, type StreamPushData } from '@/bam/namespaces/message';
import { getContent } from '@/conversation/utils/get-content';
import { ConversationStatus } from '@/common/services/conversation/base-conversation-service';
import { lvAssert } from '@byted-image/lv-bedrock/assert';
import { Emitter } from '@byted-image/lv-bedrock/event';
import type { agenterrcode } from '@/bam';

export class MessageReceiver {
  private _activeStreamingMessage?: ClientStreamingMessage;

  private _messageEventDisposable?: IDisposable;
  private _errorEventDisposable?: IDisposable;
  private readonly _onMessageError = new Emitter<[{ errCode?: agenterrcode.ErrCode; msg?: string }]>();
  private readonly _onAddMessages = new Emitter<[ClientMessage[]]>();
  // 一次只能有一个流式返回
  private readonly _onSteamingMessageReceive = new Emitter<[ClientMessage]>();

  private readonly _onStatusChange = new Emitter<[ConversationStatus]>();
  private _status = ConversationStatus.Idle;

  private _toolMessages = new Map<string, ClientToolMessage>();

  get onAddMessages() {
    return this._onAddMessages.event;
  }

  get onMessageError() {
    return this._onMessageError.event;
  }

  get onStatusChange() {
    return this._onStatusChange.event;
  }

  get onSteamingMessageReceive() {
    return this._onSteamingMessageReceive.event;
  }

  get status() {
    return this._status;
  }

  constructor(private readonly _socket: SocketClient) {
    const onMessageHandler = (ev: MessageEvent) => {
      try {
        console.log('socket message --- new message222', ev);
        if (ev.value === '') {
          return;
        }
        // console.log('socket message', ev.value);
        const data = JSON.parse(ev.value) as StreamPushData;
        console.log('socket message --- new message', data);

        if (data.type === PushType.Error) {
          console.error('socket message error', data.error_code, data.error_message);
          // this._conversationService.presentAssistantMessage();
          this._onMessageError.fire({
            errCode: data.error_code,
            msg: data.error_message,
          });
        } else if (data.type === PushType.NewMessage) {
          this._processStreamSingle(data.message!);
        } else if (data.type === PushType.Eof) {
          if (this._activeStreamingMessage) {
            this._activeStreamingMessage.isEof = true;
            this._onSteamingMessageReceive.fire(this._activeStreamingMessage);
            this.resetActiveStreamingMessage();
          }
        }
      } catch (e) {
        console.error('socket message parse failed', e);
      }
    };
    const onErrorHandler = (ev: ErrorEvent) => {
      console.error('socket error', ev);
    };
    this._messageEventDisposable = this._socket.onMessage(onMessageHandler);
    this._errorEventDisposable = this._socket.onError(onErrorHandler);
  }

  /**
   * 查找是否存在相同工具ID和代理ID的未完成工具消息
   * @param toolId 工具ID
   * @returns 找到的工具消息或undefined
   */
  private _findIncompleteToolMessage(toolId: string) {
    return this._toolMessages.get(toolId);
  }

  private _processStreamSingle(dataParsed: Message) {
    this._setStatus(ConversationStatus.Processing);

    if (dataParsed.tool_call_id) {
      this._processStreamToolCallMessage(dataParsed);
    } else if (dataParsed.content && dataParsed.content.length > 0) {
      this._processStreamContentMessage(dataParsed);
    }

    if (dataParsed.tool_calls && dataParsed.tool_calls.length > 0) {
      this._processNewToolCallMessage(dataParsed);
    }

    const finishReason = dataParsed.metadata?.finish_reason;
    if (finishReason !== undefined && finishReason === 'stop') {
      this._setStatus(ConversationStatus.Idle);
    }
  }

  /** 目前仅支持server tool call */
  private _processStreamToolCallMessage(dataParsed: Message) {
    lvAssert(dataParsed.tool_call_id, 'tool_call_id is required');
    this.resetActiveStreamingMessage();

    // 尝试查找是否有未完成的相同工具消息
    const existingToolMessage = this._findIncompleteToolMessage(dataParsed.tool_call_id);
    if (existingToolMessage) {
      // 如果找到未完成的工具消息且当前消息包含输出，则更新现有消息
      existingToolMessage.output = getContent(dataParsed.content![0]).content;
    }
  }

  private _processNewToolCallMessage(dataParsed: Message) {
    lvAssert(dataParsed.tool_calls && dataParsed.tool_calls.length > 0, 'tool_calls is required');
    this.resetActiveStreamingMessage();

    const toolMessages: ClientToolMessage[] = [];
    for (const toolCall of dataParsed.tool_calls) {
      const newToolMessage = new ClientToolMessage({
        createdAt: new Date().toString(),
        id: toolCall.id,
        name: toolCall.name ?? '',
        input: toolCall.input ?? '',
        version: dataParsed.version,
      });
      this._toolMessages.set(toolCall.id, newToolMessage);
      toolMessages.push(newToolMessage);
    }
    this._onAddMessages.fire(toolMessages);
  }

  private _processStreamContentMessage(dataParsed: Message) {
    lvAssert(dataParsed.content && dataParsed.content.length > 0, 'content is required');

    // 若 activeStreamingMessage 不存在，创建新的ClientStreamingMessage，重置流式消息和 chunk map
    if (!this._activeStreamingMessage) {
      this.resetActiveStreamingMessage();
      this._activeStreamingMessage = new ClientStreamingMessage({
        content: '',
        createdAt: new Date().toString(),
        version: dataParsed.version,
      });
      this._onAddMessages.fire([this._activeStreamingMessage]);
    }

    for (const content of dataParsed.content) {
      this._activeStreamingMessage.appendChunk(getContent(content), dataParsed.version);
    }
    this._onSteamingMessageReceive.fire(this._activeStreamingMessage);
  }

  public resetActiveStreamingMessage(): void {
    this._activeStreamingMessage = undefined;
  }

  private _setStatus(status: ConversationStatus) {
    this._status = status;
    this._onStatusChange.fire(status);
  }

  /**
   * 销毁 MessageReceiver，清理事件监听器和内部状态
   */
  public dispose() {
    // 清理事件监听器
    this._messageEventDisposable?.dispose();
    this._errorEventDisposable?.dispose();
    // 清理内部状态
    this.resetActiveStreamingMessage();
    this._toolMessages.clear();
    this._messageEventDisposable = undefined;
    this._errorEventDisposable = undefined;
  }
}
