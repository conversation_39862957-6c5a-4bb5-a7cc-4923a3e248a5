import { Role, type ClientMessage } from '@/conversation/client-message/abstract-message';
import { ClientContentMessage } from '@/conversation/client-message/content-message';
import { Emitter, type Event } from '@byted-image/lv-bedrock/event';
import { type ConversationDetail, IConversationHistoryService } from '../history';
import type { ConversationTypeEnum } from '@/common/constants/conversation-types';
import type { AgentId } from '../const';
import type { Int64 } from '@/bam';

export class MessagesManager {
  // static async loadMessagesManager(cid: string, parentCid: string, parentMessageVersion: Int64) {

  // }
  // messages
  protected readonly _messages: ClientMessage[] = [];
  protected readonly _onAppendMessages = new Emitter<[ClientMessage[]]>();
  /** 添加消息 */
  public readonly onAppendMessages: Event<[ClientMessage[]]> = this._onAppendMessages.event;
  protected readonly _onUpdateMessage = new Emitter<[ClientMessage, boolean?]>();
  /** 更新消息 */
  public readonly onUpdateMessage: Event<[ClientMessage, boolean?]> = this._onUpdateMessage.event;
  protected readonly _onPresentAssistantMessage = new Emitter<[]>();
  /** 展示Assistant消息 */
  public readonly onPresentAssistantMessage: Event<[]> = this._onPresentAssistantMessage.event;

  constructor(
    private readonly _options: {
      agentType: AgentId;
      cid: string;
      parentCid?: string;
      parentMessageVersion?: Int64;
      conversationType: ConversationTypeEnum;
    },
    messages: ClientMessage[],
    @IConversationHistoryService private readonly _historyService: IConversationHistoryService,
  ) {
    this._messages = messages;
  }

  public getMessages() {
    return this._messages;
  }

  public getMessageById(id: string) {
    return this._messages.find((m) => m.id === id);
  }

  public appendMessages(messages: ClientMessage[]) {
    this._messages.push(...messages);
    this._onAppendMessages.fire(messages);

    // 自动保存到历史记录
    this.autoSaveConversation().catch((error) => {
      console.error(`[${this._options.conversationType}] Failed to auto-save conversation:`, error);
    });
  }

  /**
   * 更新某条消息
   * @param message 消息
   */
  public updateMessage(message: ClientMessage, isEof?: boolean) {
    const index = this._messages.findIndex((m) => m.id === message.id);

    if (index !== -1) {
      this._messages[index] = message;
      this._onUpdateMessage.fire(message, isEof);

      // 自动保存到历史记录
      this.autoSaveConversation().catch((error) => {
        console.error(`[${this._options.conversationType}] Failed to auto-save conversation:`, error);
      });
    }
  }

  /** 自动保存当前会话到历史记录 */
  public async autoSaveConversation(): Promise<void> {
    const currentCid = this._options.cid; // 避免竞态
    const messages = this._getAutoSaveMessages();
    if (!this._options.cid || messages.length === 0) {
      return;
    }

    try {
      const conversationDetail = await this._buildConversationDetail(currentCid, messages);

      console.debug(
        `[${this._options.conversationType}] 正在保存会话: ${currentCid}, 消息数: ${messages.length}, 类型: ${this._options.conversationType}`,
      );
      const result = await this._historyService.saveConversation(conversationDetail);
      const [error] = result.pair();

      if (error) {
        console.error(`[${this._options.conversationType}] 保存会话失败:`, error);
      } else {
        console.debug(
          `[${this._options.conversationType}] 保存会话成功: ${currentCid}, 标题: ${conversationDetail.title}`,
        );
      }
    } catch (error) {
      console.error(`[${this._options.conversationType}] 保存会话时发生错误:`, error);
    }
  }

  protected async _buildConversationDetail(currentCid: string, messages: ClientMessage[]) {
    // 先检查会话是否已存在，如果存在则保持原有信息
    const existingResult = await this._historyService.getConversation(currentCid);
    const [existingError, existingConversation] = existingResult.pair();

    let title: string;
    let lastUpdated: string;

    if (!existingError) {
      // 会话已存在，检查是否有新消息
      const hasNewMessages = existingConversation.messageCount !== messages.length;

      title = existingConversation.title;
      lastUpdated = hasNewMessages ? new Date().toISOString() : existingConversation.lastUpdated;

      console.debug(
        `[${this._options.conversationType}] 会话已存在，保持原有标题 --- new: ${title}, ${hasNewMessages ? '有新消息，更新时间' : '无新消息，保持原时间'}`,
      );
    } else {
      // 新会话，生成新标题和更新时间
      title = this._generateConversationTitle();
      lastUpdated = new Date().toISOString();
      console.debug(`[${this._options.conversationType}] 新会话，生成标题 - messages-manager: ${title}`);
    }

    // 获取最后一条消息预览
    const lastMessage = messages[messages.length - 1];
    let preview = '';
    if (lastMessage instanceof ClientContentMessage) {
      const content = lastMessage.content.trim();
      preview = content.length > 100 ? `${content.substring(0, 100)}...` : content;
    }

    const conversationDetail: ConversationDetail = {
      id: currentCid,
      parentId: this._options.parentCid || undefined,
      title: title,
      agentType: this._options.agentType,
      lastUpdated,
      messageCount: messages.length,
      preview,
      messages: [...messages],
      conversationType: this._options.conversationType,
    };

    return conversationDetail;
  }

  protected _getAutoSaveMessages(): ClientMessage[] {
    return this._messages.slice();
  }
  /** 生成会话标题（基于第一条用户消息） */
  protected _generateConversationTitle(): string {
    const firstUserMessage = this._messages.find(
      (msg) => msg instanceof ClientContentMessage && (msg as ClientContentMessage).role === Role.User,
    ) as ClientContentMessage | undefined;

    if (firstUserMessage?.content) {
      const content = firstUserMessage.content.trim();
      if (content.length > 50) {
        return `${content.substring(0, 50)}...`;
      }
      return content;
    }

    return `${this._options.conversationType}会话 ${this._options.cid.slice(0, 8)}`;
  }
}
