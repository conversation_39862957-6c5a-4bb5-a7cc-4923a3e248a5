import type { IInstantiationService } from '@byted-image/lv-bedrock/di';
import { ConversationTypeEnum } from '@/common/constants/conversation-types';
import { type ClientMessage, Role } from '@/conversation/client-message/abstract-message';
import { ClientContentMessage } from '@/conversation/client-message/content-message';
import { INetworkClientFactoryService } from '@/common/services/network-client-factory/network-client-factory-service.interface';
import { type ILvErrorOr, makeErrorBy, makeOkWith } from '@byted-image/lv-bedrock/error';
import { Emitter, type Event } from '@byted-image/lv-bedrock/event';
import type { NetworkClientInstance } from '@byted-image/lv-bedrock/network';
import {
  EGetConversationError,
  type ConversationDetail,
  type IConversationHistoryService,
} from './history/conversation-history.interface';
import type { CreateConversationRequest } from '@/bam/namespaces/agw';
import { ConversationType } from '@/bam/namespaces/conversation';
import type { Int64 } from '@/bam';
import { AgentId } from './const';
import { IPrivateConversationService } from '@/private/private-conversation-service.interface';

// 会话状态，目前先定义两个状态，后续可以扩展
export enum ConversationStatus {
  // 空闲状态
  Idle = 'idle',
  // 处理中状态
  Processing = 'processing',
}

// 目前只有private在用
export abstract class BaseConversationService {
  protected _cid = '';
  protected _parentCid = '';
  protected _parentMessageVersion: Int64 = 0;

  // messages
  protected readonly _messages: ClientMessage[] = [];
  protected readonly _onAppendMessages = new Emitter<[ClientMessage[]]>();
  /** 添加消息 */
  public readonly onAppendMessages: Event<[ClientMessage[]]> = this._onAppendMessages.event;
  protected readonly _onUpdateMessage = new Emitter<[ClientMessage]>();
  /** 更新消息 */
  public readonly onUpdateMessage: Event<[ClientMessage]> = this._onUpdateMessage.event;
  protected readonly _onPresentAssistantMessage = new Emitter<[]>();
  /** 展示Assistant消息 */
  public readonly onPresentAssistantMessage: Event<[]> = this._onPresentAssistantMessage.event;

  // conversation status
  private _conversationStatus: ConversationStatus = ConversationStatus.Idle;
  private readonly _onConversationStatusChange = new Emitter<[ConversationStatus]>();
  public readonly onConversationStatusChange: Event<[ConversationStatus]> = this._onConversationStatusChange.event;

  // 切换会话
  private _onSwitchConversation = new Emitter<[string, messages: ClientMessage[]]>();
  /** 切换会话 */
  public readonly onSwitchConversation: Event<[string, messages: ClientMessage[]]> = this._onSwitchConversation.event;

  // 抽象属性和方法
  protected abstract readonly _conversationType: ConversationTypeEnum;
  public abstract readonly agentType: AgentId;
  protected abstract _createConversationUrl(): string;
  protected abstract _getConversationHeaders(): Record<string, string>;
  protected abstract _afterAppendMessages(messages: ClientMessage[]): void;
  protected abstract _afterPresentAssistantMessage(): void;

  // 依赖
  protected readonly _networkClient: NetworkClientInstance;

  constructor(
    private readonly _historyService: IConversationHistoryService,
    private readonly _instantiationService: IInstantiationService,
    networkClientFactoryService: INetworkClientFactoryService,
  ) {
    this._networkClient = networkClientFactoryService.build({});
  }

  public get id() {
    return this._cid;
  }

  public get parentId() {
    return this._parentCid;
  }

  public getMessages() {
    return this._messages;
  }

  public getMessageById(id: string) {
    return this._messages.find((message) => message.id === id);
  }

  public appendMessages(messages: ClientMessage[]) {
    this._messages.push(...messages);
    this._onAppendMessages.fire(messages);

    // 自动保存到历史记录
    this._autoSaveConversation().catch((error) => {
      console.error(`[${this._conversationType}] Failed to auto-save conversation:`, error);
    });

    this._afterAppendMessages(messages);
  }

  /**
   * 更新某条消息
   * @param message 消息
   */
  public updateMessage(message: ClientMessage) {
    const index = this._messages.findIndex((m) => m.id === message.id);

    if (index !== -1) {
      this._messages[index] = message;
      this._onUpdateMessage.fire(message);

      // 自动保存到历史记录
      this._autoSaveConversation().catch((error) => {
        console.error(`[${this._conversationType}] Failed to auto-save conversation:`, error);
      });
    }
  }

  public presentAssistantMessage() {
    this._onPresentAssistantMessage.fire();

    this._afterPresentAssistantMessage();
  }

  public changeConversationStatus(status: ConversationStatus) {
    this._conversationStatus = status;
    this._onConversationStatusChange.fire(status);
  }

  public getConversationStatus(): ConversationStatus {
    return this._conversationStatus;
  }

  protected async _beforeSwitchConversation(cid: string) {
    // 在切换会话前，仅在当前会话有新消息时才保存
    if (this._cid && this._messages.length > 0 && this._cid !== cid) {
      const existingResult = await this._historyService.getConversation(this._cid);
      const [existingError, existingConversation] = existingResult.pair();

      if (!existingError && existingConversation.messageCount !== this._messages.length) {
        console.log(`[${this._conversationType}] 当前会话有新消息，保存后再切换`);
        await this._autoSaveConversation();
      } else if (existingError?.code === EGetConversationError.ConversationNotFound) {
        console.log(`[${this._conversationType}] 历史会话不存在，保存后再切换`);
        await this._autoSaveConversation();
      } else {
        console.log(`[${this._conversationType}] 当前会话无新消息，直接切换`);
      }
    }
  }

  protected _afterSwitchConversation(_cid: string): Promise<void> {
    return Promise.resolve();
  }

  protected async _switchSubConversationWhenSwitchConversation(cid: string): Promise<void> {
    const relationResult = await this._historyService.getConversationRelation(cid);
    const [relationError, relation] = relationResult.pair();

    if (relationError || !relation) {
      // 现在可以忽略子会话
      return Promise.resolve();
    }

    for (const { cid, parentMessageVersion } of relation.childIds) {
      // 1. 子会话信息同步到主会话
      const messagesResult = await this._historyService.getConversationMessages(cid, 0, -1);
      const [messagesError, messages] = messagesResult.pair();

      if (messagesError || !messages) {
        continue;
      }

      const parentIndex = this._messages.findIndex((message) => message.version === parentMessageVersion);
      if (parentIndex !== -1) {
        this._messages.splice(parentIndex + 1, 0, ...messages);
      }

      // 2. 触发子会话service onSwitchConversation
      const subConversationResult = await this._historyService.getConversation(cid);
      const [subConversationError, subConversation] = subConversationResult.pair();
      if (subConversationError || !subConversation) {
        continue;
      }
      const subConversationService = this._instantiationService.invokeFunction((accessor) => {
        return accessor.get(IPrivateConversationService);
      });
      subConversationService.switchConversation(cid);
    }
  }

  /**
   * 切换会话（使用历史服务）
   * @param cid 会话id
   * @param parentCid 父会话id 默认为空
   */
  public async switchConversation(cid: string): Promise<ILvErrorOr<void>> {
    try {
      await this._beforeSwitchConversation(cid);

      const result = await this._historyService.getConversation(cid);
      const [error, conversation] = result.pair();
      if (error) {
        return makeErrorBy(-1, '获取会话失败', error);
      }

      // 验证会话类型
      if (conversation.conversationType !== this._conversationType) {
        return makeErrorBy(
          -1,
          '会话类型不匹配',
          new Error(`Expected ${this._conversationType} conversation, but got ${conversation.conversationType}`),
        );
      }

      // 切换到新会话
      this._cid = cid;
      this._parentCid = '';
      this._parentMessageVersion = 0;
      this._messages.length = 0;
      this._messages.push(...conversation.messages);

      await this._switchSubConversationWhenSwitchConversation(cid);

      console.log(`[${this._conversationType}] 已切换到会话: ${cid}, 消息数: ${this._messages.length}`);

      await this._afterSwitchConversation(cid);

      this._onSwitchConversation.fire(cid, this._messages);
      return makeOkWith(undefined);
    } catch (error) {
      return makeErrorBy(-1, `切换${this._conversationType}会话失败`, error as Error);
    }
  }

  protected _beforeReset() {
    // 保存当前会话到历史记录（如果有消息的话）
    if (this._cid && this._messages.length > 0) {
      this._autoSaveConversation().catch((error) => {
        console.error(`[${this._conversationType}] Failed to save conversation before reset:`, error);
      });
    }
  }

  protected _afterReset() {
    // nothing
  }

  /** 重置会话状态 */
  public reset() {
    this._beforeReset();

    // 重置状态
    this._cid = '';
    this._parentCid = '';
    this._parentMessageVersion = 0;
    this._messages.length = 0;
    console.log(`[${this._conversationType}] 会话状态已重置`);

    this._afterReset();
  }

  protected async _beforeCreateConversation(): Promise<ILvErrorOr<void>> {
    if (!this._cid || this._messages.length === 0) {
      return makeOkWith(undefined);
    }

    // 在创建新会话前，先保存当前会话（如果有消息的话）
    console.log(
      `[${this._conversationType}] 保存当前会话后再创建新会话，当前会话ID: ${this._cid}, 消息数: ${this._messages.length}`,
    );
    try {
      await this._autoSaveConversation();
      console.log(`[${this._conversationType}] 当前会话保存成功，准备创建新会话`);
      return makeOkWith(undefined);
    } catch (error) {
      console.error(`[${this._conversationType}] 保存当前会话失败，中止创建新会话:`, error);
      return makeErrorBy(-1, '保存当前会话失败，无法创建新会话', error as Error);
    }
  }

  protected _afterCreateConversation(_options: {
    cid: string;
    parentConversationId: string;
    parentMessageVersion: Int64;
  }): Promise<void> {
    return Promise.resolve();
  }

  protected _getConversationData(options: {
    parentConversationId: string;
    parentMessageVersion: Int64;
  }): CreateConversationRequest {
    const { parentConversationId, parentMessageVersion } = options;

    const typeMap = {
      [ConversationTypeEnum.TechDocs]: ConversationType.Arch,
      [ConversationTypeEnum.Coding]: ConversationType.Coding,
      [ConversationTypeEnum.D2c]: ConversationType.D2c,
      [ConversationTypeEnum.Understanding]: ConversationType.Understanding,
    };

    // @ts-ignore
    const data: CreateConversationRequest = {
      type: typeMap[this._conversationType] || ConversationType.Unknown,
    };

    if (parentConversationId && parentMessageVersion) {
      data.parent_id = parentConversationId;
      data.parent_version = parentMessageVersion;
    }

    return data;
  }

  /** 保存会话关系 */
  protected async _updateConversationRelation(options: {
    cid: string;
    parentConversationId: string;
    parentMessageVersion: Int64;
  }) {
    const { cid, parentConversationId, parentMessageVersion } = options;

    if (parentConversationId && parentMessageVersion) {
      try {
        await this._historyService.saveConversationRelation(options);
        console.debug(`[${this._conversationType}] 保存会话关系成功: ${parentConversationId}, ${cid}`);
      } catch (error) {
        console.error(`[${this._conversationType}] 保存会话关系失败:`, error);
      }
    }
  }

  /** 创建会话的通用逻辑 */
  public async createConversation(options?: {
    parentConversationId: string;
    parentMessageVersion: Int64;
  }): Promise<ILvErrorOr<string>> {
    try {
      const { parentConversationId = '', parentMessageVersion = 0 } = options || {};
      const beforeCreateResult = await this._beforeCreateConversation();
      if (!beforeCreateResult.ok) {
        return beforeCreateResult;
      }

      const url = this._createConversationUrl();
      const headers = this._getConversationHeaders();
      const data = this._getConversationData({
        parentConversationId,
        parentMessageVersion,
      });

      console.log(`[${this._conversationType}] 创建会话前: ${JSON.stringify(data)}`);

      const response = await this._networkClient(url, {
        method: 'POST',
        headers,
        data,
      });

      console.log(`[${this._conversationType}] 创建会话: ${JSON.stringify(response.headers)}`);

      const cid = response.data.data.id;

      await this._updateConversationRelation({
        cid,
        parentConversationId,
        parentMessageVersion,
      });

      // 切换到新会话
      this._cid = cid;
      this._parentCid = parentConversationId;
      this._parentMessageVersion = parentMessageVersion;
      this._messages.length = 0;

      console.log(`[${this._conversationType}] 创建会话成功: ${cid}, 类型: ${this._conversationType}`);

      await this._afterCreateConversation({
        cid,
        parentConversationId,
        parentMessageVersion,
      });

      return makeOkWith(cid);
    } catch (error) {
      console.log(`[${this._conversationType}] 创建会话失败: ${error}`);
      return makeErrorBy(-1, '创建会话失败', error as Error);
    }
  }

  public async save(): Promise<void> {
    return this._autoSaveConversation();
  }

  protected _getAutoSaveMessages(): ClientMessage[] {
    return this._messages.slice();
  }

  /** 生成会话标题（基于第一条用户消息） */
  protected _generateConversationTitle(): string {
    const firstUserMessage = this._messages.find(
      (msg) => msg instanceof ClientContentMessage && (msg as ClientContentMessage).role === Role.User,
    ) as ClientContentMessage | undefined;

    if (firstUserMessage?.content) {
      const content = firstUserMessage.content.trim();
      if (content.length > 50) {
        return `${content.substring(0, 50)}...`;
      }
      return content;
    }

    return `${this._conversationType}会话 ${this._cid.slice(0, 8)}`;
  }

  protected async _buildConversationDetail(currentCid: string, messages: ClientMessage[]) {
    // 先检查会话是否已存在，如果存在则保持原有信息
    const existingResult = await this._historyService.getConversation(currentCid);
    const [existingError, existingConversation] = existingResult.pair();

    let title: string;
    let lastUpdated: string;

    if (!existingError) {
      // 会话已存在，检查是否有新消息
      const hasNewMessages = existingConversation.messageCount !== messages.length;

      title = existingConversation.title;
      lastUpdated = hasNewMessages ? new Date().toISOString() : existingConversation.lastUpdated;

      console.debug(
        `[${this._conversationType}] 会话已存在，保持原有标题: ${title}, ${hasNewMessages ? '有新消息，更新时间' : '无新消息，保持原时间'}`,
      );
    } else {
      // 新会话，生成新标题和更新时间
      title = this._generateConversationTitle();
      lastUpdated = new Date().toISOString();
      console.debug(`[${this._conversationType}] 新会话，生成标题: ${title}`);
    }

    // 获取最后一条消息预览
    const lastMessage = messages[messages.length - 1];
    let preview = '';
    if (lastMessage instanceof ClientContentMessage) {
      const content = lastMessage.content.trim();
      preview = content.length > 100 ? `${content.substring(0, 100)}...` : content;
    }

    const conversationDetail: ConversationDetail = {
      id: currentCid,
      parentId: this._parentCid || undefined,
      title: title,
      agentType: this.agentType,
      lastUpdated,
      messageCount: messages.length,
      preview,
      messages: [...messages],
      conversationType: this._conversationType,
    };

    return conversationDetail;
  }

  /** 自动保存当前会话到历史记录 */
  protected async _autoSaveConversation(): Promise<void> {
    const currentCid = this._cid; // 避免竞态
    const messages = this._getAutoSaveMessages();
    if (!this._cid || messages.length === 0) {
      return;
    }

    try {
      const conversationDetail = await this._buildConversationDetail(currentCid, messages);

      console.debug(
        `[${this._conversationType}] 正在保存会话: ${currentCid}, 消息数: $messages.length, 类型: ${this._conversationType}`,
      );
      const result = await this._historyService.saveConversation(conversationDetail);
      const [error] = result.pair();

      if (error) {
        console.error(`[${this._conversationType}] 保存会话失败:`, error);
      } else {
        console.debug(`[${this._conversationType}] 保存会话成功: ${currentCid}, 标题: ${conversationDetail.title}`);
      }
    } catch (error) {
      console.error(`[${this._conversationType}] 保存会话时发生错误:`, error);
    }
  }

  protected async _restoreConversation() {}
}
