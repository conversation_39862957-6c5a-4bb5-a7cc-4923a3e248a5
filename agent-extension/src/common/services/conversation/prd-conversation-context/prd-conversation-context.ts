import { ConversationTypeEnum } from '@/common/constants/conversation-types';
import { BaseConversationContext } from '../base-conversation-context/base-conversation-context';
import { AgentId } from '../const';
import { DefaultModelType } from '../../prd-chat/chat-service.interface';
import { IStorageService } from '../../storage/storage-service.interface';
import type { Int64 } from '@/bam';
import { IInstantiationService } from '@byted-image/lv-bedrock/di';
import { ISocketService } from '../../socket/socket-service.interface';
import { D2C_SETTINGS_STORAGE_KEY, PLANNING_MODEL_KEY } from '../../d2c-chat/settings';
import { AskMessageRole, type AskMessage } from '../../base-chat/types';
import type { ClientToolMessage } from '@/conversation/client-message/tool-message';
import { AskUserTool } from '@/tools/ask-user/ask-user';
import type { Tool } from '@/tools/base';
import { IMcpHubService } from '../../mcp-hub/mcp-hub-service.interface';
import { IRulesService, RuleType } from '../../rules/rules-service.interface';
import { getSystemEnv } from '@/common/env/system';
import type { AgentInferenceParams } from '@/bam/namespaces/agentserver';
import * as vscode from 'vscode';
import { INetworkClientFactoryService } from '../../network-client-factory/network-client-factory-service.interface';
import { ChatErrorCode } from '../../base-chat/chat-error';
import { makeError, makeErrorBy, makeOk, type ILvErrorOr } from '@byted-image/lv-bedrock/error';
import { Emitter } from '@byted-image/lv-bedrock/event';
import { ClientMessageType, type ClientMessage } from '@/conversation/client-message/abstract-message';
import { GrepSearchTool } from '@/tools/grep-search';
import { ListDirectoryTool } from '@/tools/list-directory';
import { ReadFileTool } from '@/tools/read-file';
import { RecordPlanTool } from '@/tools/record-plan/record-plan';
import { DesignDetailTool } from '@/tools/design-detail';
import { IWorkspaceService } from '../../workspace/workspace.interface';
import { ModelType } from '@/bam/namespaces/base';
import type { RetryCodeAgentRequest } from '@/bam/namespaces/agw';

export class PrdConversationContext extends BaseConversationContext {
  protected readonly _conversationType = ConversationTypeEnum.TechDocs;
  protected readonly _agentType = AgentId.PrdAgent;
  public readonly _onSendMessageError = new Emitter<ILvErrorOr<void>[]>();

  private _settings: any = {};

  get onSendMessageError() {
    return this._onSendMessageError.event;
  }

  get messages() {
    return this._messagesManager.getMessages();
  }

  constructor(
    options: { cid: string; parentCid?: string; parentMessageVersion?: Int64 },
    initMessages: ClientMessage[],
    @IInstantiationService _instantiationService: IInstantiationService,
    @ISocketService _socketService: ISocketService,
    @IStorageService protected readonly _storageService: IStorageService,
    @IMcpHubService private readonly _mcpHubService: IMcpHubService,
    @IRulesService private readonly _rulesService: IRulesService,
    @INetworkClientFactoryService _networkClientFactoryService: INetworkClientFactoryService,
    @IWorkspaceService private readonly _workspaceService: IWorkspaceService,
  ) {
    super(options, initMessages, _instantiationService, _socketService, _networkClientFactoryService, _storageService);
  }

  public async bootstrap(): Promise<void> {
    await super.bootstrap();
    this._messageReceiver.onAddMessages((messages) => {
      const toolMessages = messages.filter((message) => message.type === ClientMessageType.Tool);
      this._handleToolMessages(toolMessages as ClientToolMessage[]);
    });
    const settings = await this._storageService.get(D2C_SETTINGS_STORAGE_KEY);
    if (!settings) {
      return;
    }
    this._settings = JSON.parse(settings);
  }

  async sendMessage(message: AskMessage) {
    console.log('CodingConversationContext.sendMessage', message);
    const alwaysRulesContent = await this._rulesService.getRulesContentByType(RuleType.Always);

    const repoInfos = Array.from(this._workspaceService.getAllRepoInfo().values());

    const modelType = await this._getCurrentModel();
    const business = await this.getBusiness();
    const askParams = {
      params: {
        id: this._options.cid,
        client_env: {
          system_env: getSystemEnv(repoInfos),
          tools: this._getTools()
            .filter((tool) => tool.enable())
            .map((tool) => tool.getInfo()),
          project_rules: alwaysRulesContent,
          figma_token: this._settings.figma_token,
          did: this._workspaceService.getDid(),
          repo_infos: repoInfos.map((info) => ({
            branch: info.branch,
            path: info.path,
            repo_name: info.repoName,
            path_list: this._workspaceService.getBusinessPathList(info.repoName, business),
          })),
        },
        model_type: modelType,
      } as AgentInferenceParams,
    };
    if (message.role === AskMessageRole.User) {
      askParams.params.user_message = {
        content: message.userContent,
      };
    } else {
      askParams.params.tool_messages = message.toolsData.map((data) => ({
        content: data.content,
        request_id: data.requestId,
      }));
    }

    console.log('prd askParams', askParams);

    try {
      const resp = await this._networkClient('https://capcut-devops.byted.org/conversation/plan', {
        method: 'POST',
        data: JSON.stringify(askParams),
      });

      if (resp.data.code !== 0) {
        vscode.window.showErrorMessage(`send message error: [${resp.data.code}]-${resp.data.message}`);
        const sendMessageError = makeError(
          ChatErrorCode.SendMessageFailed,
          `send message failed: ${resp.data.message}`,
        );
        this._onSendMessageError.fire(sendMessageError);
        return sendMessageError;
      }
      this._updateMessageFromLocal(message, resp.headers['x-tt-logid']);
    } catch (e) {
      console.error('send message error: ', e);
      const sendMessageError = makeErrorBy(ChatErrorCode.SendMessageFailed, 'send message failed', e as Error);
      this._onSendMessageError.fire(sendMessageError);
      return sendMessageError;
    }
    return makeOk();
  }

  protected async _getModelQueue(): Promise<ModelType[]> {
    const defaultModel = await this._getDefaultModelType();
    return [defaultModel, ModelType.Claude, ModelType.Kimi_K2, ModelType.DeepSeek];
  }

  private async _getDefaultModelType() {
    try {
      const modelSetting = await this._storageService.get(PLANNING_MODEL_KEY);
      if (!modelSetting) {
        return DefaultModelType;
      }
      const modelType = Number(JSON.parse(modelSetting).modelType ?? DefaultModelType);
      return modelType;
    } catch {
      return DefaultModelType;
    }
  }

  private _getTools(): Tool[] {
    return [
      this._instantiationService.createInstance(ListDirectoryTool),
      this._instantiationService.createInstance(GrepSearchTool),
      this._instantiationService.createInstance(ReadFileTool),
      this._instantiationService.createInstance(AskUserTool),
      this._instantiationService.createInstance(RecordPlanTool),
      this._instantiationService.createInstance(DesignDetailTool),
      ...this._mcpHubService.formatToToolInfos(),
    ];
  }

  private async _handleToolMessages(messages: ClientToolMessage[]) {
    const clientToolRunners: Promise<{ content: string; requestId: string }>[] = [];

    for (const toolMessage of messages) {
      const tool = this._getTools().find((tool) => tool.getInfo().name === toolMessage.name);
      if (!tool) continue;

      const runner = async () => {
        const result = await tool.run(toolMessage.input ?? '', toolMessage.id, this._options.cid, toolMessage.version);
        return {
          content: result.value,
          requestId: toolMessage.id,
        };
      };

      clientToolRunners.push(runner());
    }

    if (clientToolRunners.length === 0) return;

    const toolsData = await Promise.all(clientToolRunners);
    console.log('PlanningChatService.handleToolMessage0', toolsData);

    this.sendMessage({
      cid: this._options.cid,
      role: AskMessageRole.Tool,
      toolsData,
    });
  }

  protected async _retryWhenMessageError(): Promise<void> {
    try {
      const alwaysRulesContent = await this._rulesService.getRulesContentByType(RuleType.Always);
      const retryParams = {
        params: {
          id: this._options.cid,
          model_type: await this._getCurrentModel(),
          client_env: {
            system_env: getSystemEnv(),
            tools: this._getTools()
              .filter((tool) => tool.enable())
              .map((tool) => tool.getInfo()),
            project_rules: alwaysRulesContent,
            figma_token: this._settings.figma_token,
          },
        },
      } as RetryCodeAgentRequest;

      const resp = await this._networkClient('https://capcut-devops.byted.org/conversation/retry-plan', {
        method: 'POST',
        data: JSON.stringify(retryParams),
      });

      if (resp.data.code !== 0) {
        vscode.window.showErrorMessage(`retry message error: [${resp.data.code}]-${resp.data.message}`);
        const sendMessageError = makeError(
          ChatErrorCode.SendMessageFailed,
          `retry message failed: ${resp.data.message}`,
        );
        this._onSendMessageError.fire(sendMessageError);
      }
    } catch (e) {
      console.error('retry message error: ', e);
      const sendMessageError = makeErrorBy(ChatErrorCode.SendMessageFailed, 'retry message failed', e as Error);
      this._onSendMessageError.fire(sendMessageError);
    }
  }
}
