import type { FileNode } from './types';
import type { IWorkspaceFilesService } from './workspace-files.interface';
import { RecentFileManager } from './recent-file';
import { IInstantiationService } from '@byted-image/lv-bedrock/di';
import { FileType, Uri, workspace } from 'vscode';
import path from 'node:path';
import { searchWorkspaceFiles } from './search/file-search';

export class WorkspaceFilesService implements IWorkspaceFilesService {
  _serviceBrand: undefined;
  private _recentFilesManager: RecentFileManager;
  private gitignorePatterns: string[] = [];
  private workspaceRoot = '';

  constructor(@IInstantiationService private readonly _instantiationService: IInstantiationService) {
    this._recentFilesManager = this._instantiationService.createInstance(RecentFileManager);
  }

  public init() {
    // this._fileTreeManager.init();

    const workspaceFolders = workspace.workspaceFolders;

    if (workspaceFolders) {
      // 设置workspace根目录
      this.workspaceRoot = workspaceFolders[0].uri.fsPath;
      // 为每个工作区加载.gitignore
      Promise.all(workspaceFolders.map((folder) => this.loadGitignore(folder.uri))).then(() => {
        this._recentFilesManager.init(this.gitignorePatterns);
      });
    }
  }

  // 目前没有启动流程，只能做异步处理
  public async searchFileOrDir(keyword: string): Promise<FileNode[]> {
    // 使用 workspace.findFiles 搜索文件和文件夹
    const ignorePatterns =
      this.gitignorePatterns.length > 0 ? `{${this.gitignorePatterns.join(',')},**/.*}` : '**/node_modules/**,**/.*';
    const files = await workspace.findFiles(`**/*${keyword}*`, ignorePatterns, 25);

    // 优化后的代码：合并文件和目录的处理逻辑，避免重复查找和遍历，提升可读性和效率
    const [fileStats, searchResults] = await Promise.all([
      Promise.all(
        files.map(async (item) => {
          try {
            const stat = await workspace.fs.stat(item);
            return {
              uri: item,
              stat,
            };
          } catch (error) {
            console.error('Error reading file or directory stats', error);
            return null;
          }
        }),
      ),
      searchWorkspaceFiles(keyword, this.workspaceRoot),
    ]);

    // 处理文件节点
    const fileNodes: FileNode[] = fileStats.filter(Boolean).map(({ uri, stat }: any) => {
      const relativePath = this.getRelativePath(uri.fsPath);
      return {
        name: uri.path.split('/').pop() || '',
        path: uri.fsPath,
        relativePath,
        type: stat.type === FileType.Directory ? 'directory' : 'file',
      } as FileNode;
    });

    // 处理目录节点，去重（避免和 fileNodes 里的目录重复）
    const dirSet = new Set(fileNodes.filter((n) => n.type === 'directory').map((n) => n.path));
    const dirNodes: FileNode[] = searchResults
      .filter((item) => item.type === 'folder' && !dirSet.has(item.path))
      .map(({ path }) => {
        const relativePath = this.getRelativePath(path);
        return {
          name: path.split('/').pop() || '',
          path,
          type: 'directory',
          relativePath,
        } as FileNode;
      });

    return [...fileNodes, ...dirNodes];
  }

  public get recentVisit() {
    return this._recentFilesManager.getRecentFiles();
  }

  public get onRecentVisitChange() {
    return this._recentFilesManager.onRecentVisitChange;
  }

  public async readFileContent(path: string): Promise<string | null> {
    try {
      const uri = Uri.file(path);
      const content = await workspace.fs.readFile(uri);
      return content.toString();
    } catch (error) {
      console.error('readFileContent error', error);
      return null;
    }
  }

  public async getFolderListContent(path: string): Promise<string | null> {
    try {
      const uri = Uri.file(path);
      const entries = await workspace.fs.readDirectory(uri);
      const result = await Promise.all(
        entries.map(async ([name, type]) => {
          const entryUri = Uri.joinPath(uri, name);
          const relativePath = this.getRelativePath(entryUri.fsPath);

          // Filter out ignored files based on gitignore rules
          if (
            this.gitignorePatterns.length > 0 &&
            this.gitignorePatterns.some((pattern) => relativePath.includes(pattern))
          ) {
            return null;
          }

          let extraInfo = '';
          if (type === FileType.File) {
            try {
              const stat = await workspace.fs.stat(entryUri);
              const fileContent = await workspace.fs.readFile(entryUri);
              const lineCount = fileContent.toString().split('\n').length;
              extraInfo = `${(stat.size / 1024).toFixed(1)}KB, ${lineCount} lines`;
            } catch (error) {
              console.error('Error retrieving file line count', error);
            }
          }
          const typeStr = type === FileType.Directory ? '[dir]' : '[file]';

          if (type === FileType.Directory) {
            return `${typeStr} ${name}/ (? items)`;
          }
          return `${typeStr} ${name} (${extraInfo})`;
        }),
      );
      return result.filter((item) => item !== null).join('\n');
    } catch (error) {
      console.error('getFolderListContent error', error);
      return null;
    }
  }

  /**
   * 读取并解析.gitignore文件
   */
  private async loadGitignore(workspaceUri: Uri): Promise<void> {
    try {
      const gitignoreUri = Uri.joinPath(workspaceUri, '.gitignore');
      const content = await workspace.fs.readFile(gitignoreUri);
      const patterns = content
        .toString()
        .split('\n')
        .map((line) => line.trim())
        .filter((line) => line && !line.startsWith('#'));
      this.gitignorePatterns = patterns;
    } catch (error) {
      console.log('No .gitignore file found or error reading it');
      this.gitignorePatterns = [];
    }
  }

  private getRelativePath(absolutePath: string): string {
    return path.relative(this.workspaceRoot, absolutePath);
  }
}
