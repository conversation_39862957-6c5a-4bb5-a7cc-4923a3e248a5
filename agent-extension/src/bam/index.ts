// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as agentclient from "./namespaces/agentclient";
import * as agenterrcode from "./namespaces/agenterrcode";
import * as agentserver from "./namespaces/agentserver";
import * as agw from "./namespaces/agw";
import * as asset from "./namespaces/asset";
import * as base from "./namespaces/base";
import * as benchmark from "./namespaces/benchmark";
import * as coding from "./namespaces/coding";
import * as conversation from "./namespaces/conversation";
import * as d2c from "./namespaces/d2c";
import * as message from "./namespaces/message";
import * as prd from "./namespaces/prd";
import * as userinput from "./namespaces/userinput";

export {
  agentclient,
  agenterrcode,
  agentserver,
  agw,
  asset,
  base,
  benchmark,
  coding,
  conversation,
  d2c,
  message,
  prd,
  userinput,
};

export type Int64 = string | number;

export default class BamService<T> {
  private request: any = () => {
    throw new Error("BamService.request is undefined");
  };
  private baseURL: string | ((path: string) => string) = "";

  constructor(options?: {
    baseURL?: string | ((path: string) => string);
    request?<R>(
      params: {
        url: string;
        method: "GET" | "DELETE" | "POST" | "PUT" | "PATCH";
        data?: any;
        params?: any;
        headers?: any;
      },
      options?: T
    ): Promise<R>;
  }) {
    this.request = options?.request || this.request;
    this.baseURL = options?.baseURL || "";
  }

  private genBaseURL(path: string) {
    return typeof this.baseURL === "string" ? this.baseURL + path : this.baseURL(path);
  }

  /**
   * POST /conversation/benchmark
   *
   * 对话benchmark agent
   */
  AskBenchmarkAgent(
    req: agw.AskBenchmarkAgentRequest,
    options?: T
  ): Promise<agw.AskBenchmarkAgentResponse> {
    const _req = req;
    const url = this.genBaseURL("/conversation/benchmark");
    const method = "POST";
    const data = { params: _req["params"] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /conversation/code
   *
   * 对话代码生成agent
   */
  AskCodeAgent(req: agw.AskCodeAgentRequest, options?: T): Promise<agw.AskCodeAgentResponse> {
    const _req = req;
    const url = this.genBaseURL("/conversation/code");
    const method = "POST";
    const data = { params: _req["params"] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /conversation/codereview
   *
   * 对话codereview agent
   */
  AskCodeReviewAgent(
    req: agw.AskCodeReviewAgentRequest,
    options?: T
  ): Promise<agw.AskCodeReviewAgentResponse> {
    const _req = req;
    const url = this.genBaseURL("/conversation/codereview");
    const method = "POST";
    const data = { params: _req["params"] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /conversation/d2c
   *
   * 对话d2c agent
   */
  AskD2cAgent(req: agw.AskD2cAgentRequest, options?: T): Promise<agw.AskD2cAgentResponse> {
    const _req = req;
    const url = this.genBaseURL("/conversation/d2c");
    const method = "POST";
    const data = { params: _req["params"] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /conversation/plan
   *
   * 对话规划agent
   */
  AskPlanAgent(req: agw.AskPlanAgentRequest, options?: T): Promise<agw.AskPlanAgentResponse> {
    const _req = req;
    const url = this.genBaseURL("/conversation/plan");
    const method = "POST";
    const data = { params: _req["params"] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /conversation/understanding
   *
   * 对话理解需求agent
   */
  AskUnderstandAgent(
    req: agw.AskUnderstandAgentRequest,
    options?: T
  ): Promise<agw.AskUnderstandAgentResponse> {
    const _req = req;
    const url = this.genBaseURL("/conversation/understanding");
    const method = "POST";
    const data = { params: _req["params"] };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /conversation/tokens
   *
   * 计算会话token
   */
  CalcConversationTokens(
    req: agw.CalcConversationTokensRequest,
    options?: T
  ): Promise<agw.CalcConversationTokensResponse> {
    const _req = req;
    const url = this.genBaseURL("/conversation/tokens");
    const method = "GET";
    const params = { conversation_ids: _req["conversation_ids"] };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /conversation/cancel
   *
   * 取消会话
   */
  CancelConversation(
    req: agw.CancelConversationRequest,
    options?: T
  ): Promise<agw.CancelConversationResponse> {
    const _req = req;
    const url = this.genBaseURL("/conversation/cancel");
    const method = "POST";
    const data = { id: _req["id"] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /conversation/create
   *
   * 创建会话
   */
  CreateConversation(
    req: agw.CreateConversationRequest,
    options?: T
  ): Promise<agw.CreateConversationResponse> {
    const _req = req;
    const url = this.genBaseURL("/conversation/create");
    const method = "POST";
    const data = {
      type: _req["type"],
      parent_id: _req["parent_id"],
      parent_version: _req["parent_version"],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /conversation/history
   *
   * 获取会话
   */
  GetConversation(
    req: agw.GetConversationRequest,
    options?: T
  ): Promise<agw.GetConversationResponse> {
    const _req = req;
    const url = this.genBaseURL("/conversation/history");
    const method = "GET";
    const params = { Id: _req["Id"], Message: _req["Message"] };
    return this.request({ url, method, params }, options);
  }

  /**
   * GET /conversation/list
   *
   * 获取历史会话列表
   */
  GetConversationList(
    req?: agw.GetConversationListRequest,
    options?: T
  ): Promise<agw.GetConversationListResponse> {
    const _req = req || {};
    const url = this.genBaseURL("/conversation/list");
    const method = "GET";
    const params = { StartTime: _req["StartTime"], EndTime: _req["EndTime"] };
    return this.request({ url, method, params }, options);
  }

  /**
   * GET /conversation/stream-range
   *
   * 获取会话流式范围版本
   */
  GetStreamRange(req: agw.GetStreamRangeRequest, options?: T): Promise<agw.GetStreamRangeResponse> {
    const _req = req;
    const url = this.genBaseURL("/conversation/stream-range");
    const method = "GET";
    const params = { Id: _req["Id"], Start: _req["Start"], End: _req["End"] };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /conversation/retry-benchmark
   *
   * 重试对话benchmark agent
   */
  RetryBenchmarkAgent(
    req: agw.RetryBenchmarkAgentRequest,
    options?: T
  ): Promise<agw.RetryBenchmarkAgentResponse> {
    const _req = req;
    const url = this.genBaseURL("/conversation/retry-benchmark");
    const method = "POST";
    const data = { params: _req["params"] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /conversation/retry-code
   *
   * 重试对话代码生成agent
   */
  RetryCodeAgent(req: agw.RetryCodeAgentRequest, options?: T): Promise<agw.RetryCodeAgentResponse> {
    const _req = req;
    const url = this.genBaseURL("/conversation/retry-code");
    const method = "POST";
    const data = { params: _req["params"] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /conversation/retry-d2c
   *
   * 重试对话d2c agent
   */
  RetryD2cAgent(req: agw.RetryD2cAgentRequest, options?: T): Promise<agw.RetryD2cAgentResponse> {
    const _req = req;
    const url = this.genBaseURL("/conversation/retry-d2c");
    const method = "POST";
    const data = { params: _req["params"] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /conversation/retry-plan
   *
   * 重试对话规划agent
   */
  RetryPlanAgent(req: agw.RetryPlanAgentRequest, options?: T): Promise<agw.RetryPlanAgentResponse> {
    const _req = req;
    const url = this.genBaseURL("/conversation/retry-plan");
    const method = "POST";
    const data = { params: _req["params"] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /conversation/retry-understanding
   *
   * 重试对话理解需求agent
   */
  RetryUnderstandAgent(
    req: agw.RetryUnderstandAgentRequest,
    options?: T
  ): Promise<agw.RetryUnderstandAgentResponse> {
    const _req = req;
    const url = this.genBaseURL("/conversation/retry-understanding");
    const method = "POST";
    const data = { params: _req["params"] };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /ws
   *
   * 标注socket接口，让bam强制生成相关的struct
   */
  Socket(req: agentserver.AgentInferenceParams, options?: T): Promise<message.StreamPushData> {
    const _req = req;
    const url = this.genBaseURL("/ws");
    const method = "GET";
    const params = {
      id: _req["id"],
      user_message: _req["user_message"],
      tool_messages: _req["tool_messages"],
      model_type: _req["model_type"],
      client_env: _req["client_env"],
    };
    return this.request({ url, method, params }, options);
  }
}
/* eslint-enable */
