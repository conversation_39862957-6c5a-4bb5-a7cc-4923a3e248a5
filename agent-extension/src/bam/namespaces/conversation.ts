// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

export enum ConversationStatus {
  Waiting = 0,
  Executing = 1,
  Failed = 2,
  Success = 3,
  Canceled = 4,
}

export enum ConversationType {
  Unknown = 0,
  Arch = 1,
  Coding = 2,
  D2c = 3,
  CodeReview = 4,
  Benchmark = 5,
  Understanding = 6,
}

export interface Conversation {
  id: string;
  uid: string;
  title?: string;
  type: ConversationType;
  message_version: Int64;
  status: ConversationStatus;
  created_at?: Int64;
  updated_at?: Int64;
}
/* eslint-enable */
