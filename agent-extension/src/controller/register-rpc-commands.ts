import type { UserInput } from '@/bam/namespaces/userinput';
import { ModelType } from '@/common/constants/model-types';
import { IAccountService } from '@/common/services/account/account-service.interface';
import { DefaultModelType as CodingDefaultModelType } from '@/common/services/coding-chat/coding-chat-service.interface';
import { IConversationCommandsService } from '@/common/services/commands/conversation-commands.interface';
import { ID2cChatService } from '@/common/services/d2c-chat/chat-service.interface';
import { CODING_MODEL_KEY, PLANNING_MODEL_KEY, UNDERSTANDING_MODEL_KEY } from '@/common/services/d2c-chat/settings';
import { IGitService } from '@/common/services/git/git-service.interface';
import { IMcpHubService } from '@/common/services/mcp-hub/mcp-hub-service.interface';
import { DefaultModelType as PrdDefaultModelType } from '@/common/services/prd-chat/chat-service.interface';
import { IRpcService } from '@/common/services/rpc/rpc-service.interface';
import { IRulesService } from '@/common/services/rules/rules-service.interface';
import { IStorageService } from '@/common/services/storage/storage-service.interface';
import { DefaultModelType as UnderstandingDefaultModelType } from '@/common/services/understanding-chat/understanding-service.interface';
import { IWorkspaceFilesService } from '@/common/services/workspace-files/workspace-files.interface';
import type { ClientMessageType } from '@/conversation/client-message/abstract-message';
import { slardarInstance } from '@/entry/task/init-slardar-in-extension';
import askUserInteractor from '@/tools/ask-user/ask-user-interactor';
import planInteractor from '@/tools/record-plan/plan-interactor';
import type { IInstantiationService } from '@byted-image/lv-bedrock/di';
import { commands, window } from 'vscode';
import { Controller } from './controller';
import { ConversationTypeEnum } from '@/common/constants/conversation-types';

export function registerRpcCommands(controller: Controller, instantiationService: IInstantiationService) {
  const [
    rpcService,
    accountService,
    workspaceFilesService,
    d2cChatService,
    mcpHubService,
    rulesService,
    conversationCommandsService,
    gitService,
  ] = instantiationService.invokeFunction((accessor) => {
    return [
      accessor.get(IRpcService),
      accessor.get(IAccountService),
      accessor.get(IWorkspaceFilesService),
      accessor.get(ID2cChatService),
      accessor.get(IMcpHubService),
      accessor.get(IRulesService),
      accessor.get(IConversationCommandsService),
      accessor.get(IGitService),
    ];
  });
  rpcService.register('agent.showInformationMessage', (params) => {
    window.showInformationMessage(params.content, {
      detail: params.detail,
      modal: params.modal ?? false,
    });
  });

  rpcService.register('agent.getUserInfo', () => {
    return accountService.getUserInfo() || null;
  });

  rpcService.register('workspace.readFileContent', async (params: { path: string }) => {
    const content = await workspaceFilesService.readFileContent(params.path);
    return { content };
  });

  rpcService.register('workspace.readFolderListContent', async (params: { path: string }) => {
    const content = await workspaceFilesService.getFolderListContent(params.path);
    return { content };
  });

  rpcService.register('coding.createConversation', async () => {
    const result = await conversationCommandsService.newConversation(ConversationTypeEnum.Coding);
    const [error, cid] = result.pair();
    if (error) {
      return null;
    }
    return cid;
  });

  rpcService.register('understanding.createConversation', async () => {
    const result = await conversationCommandsService.newConversation(ConversationTypeEnum.Understanding);
    const [error, cid] = result.pair();
    if (error) {
      return null;
    }
    return cid;
  });

  rpcService.register('techDocs.createConversation', async () => {
    const result = await conversationCommandsService.newConversation(ConversationTypeEnum.TechDocs);
    const [error, cid] = result.pair();
    if (error) {
      return null;
    }
    return cid;
  });

  rpcService.register('d2c.createConversation', async () => {
    const result = await conversationCommandsService.newConversation(ConversationTypeEnum.D2c);
    const [error, cid] = result.pair();
    if (error) {
      return null;
    }
    return cid;
  });

  rpcService.register(
    'techDocs.sendMessage',
    (params: {
      text: UserInput;
      model_type: ModelType;
      messageType: ClientMessageType;
    }) => {
      controller.sendTechDocsMessage(params.text);
    },
  );

  rpcService.register(
    'coding.sendMessage',
    (params: {
      cid?: string;
      text: UserInput;
      model_type: ModelType;
      messageType: ClientMessageType;
    }) => {
      controller.sendCodingMessage(params.text, params.cid);
    },
  );

  rpcService.register(
    'understanding.sendMessage',
    (params: {
      cid?: string;
      text: UserInput;
      model_type: ModelType;
      messageType: ClientMessageType;
    }) => {
      controller.sendUnderstandingMessage(params.text, params.cid);
    },
  );

  // rpcService.register(
  //   'codeReview.sendMessage',
  //   (params: {
  //     text: UserInput;
  //     model_type: ModelType;
  //     messageType: ClientMessageType;
  //   }) => {
  //     controller.sendCodeReviewMessage(params.text);
  //   },
  // );

  rpcService.register(
    'd2c.sendMessage',
    (params: {
      text: UserInput;
      model_type: ModelType;
      messageType: ClientMessageType;
    }) => {
      controller.sendD2cMessage(params.text);
    },
  );

  rpcService.register(
    'private.sendMessage',
    (params: {
      text: UserInput;
      model_type: ModelType;
      messageType: ClientMessageType;
    }) => {
      controller.sendPrivateMessage(params.text);
    },
  );

  rpcService.register(
    'setConversationBusiness',
    async (params: { cid: string; business: string }) => {
      await controller.setConversationBusiness(params.cid, params.business);
    },
  );

  rpcService.register('getConversationBusiness', async (params: { cid: string }) => {
    return await controller.getConversationBusiness(params.cid);
  });

  rpcService.register('getConversationBusinessList', () => {
    return controller.getConversationBusinessList();
  });

  // === Legacy message handling migrated from WebviewProvider ===
  rpcService.register('requestCurrentConversationState', (params) => {
    controller.getCurrentConversationState(params.conversationType, params.conversationId);
  });
  rpcService.register('closeSettingsWebview', () => {
    commands.executeCommand('codin.webview.settings.close');
  });
  rpcService.register('loginAccount', () => {
    commands.executeCommand('codin.webview.account.login');
  });
  rpcService.register('logoutAccount', () => {
    commands.executeCommand('codin.webview.account.logout');
  });
  rpcService.register('requestUserInfo', () => {
    rpcService.notify('updateUserInfo', accountService.getUserInfo());
  });
  rpcService.register('requestChangedFiles', () => {
    rpcService.notify('updateChangedFiles', gitService.getChangedFiles());
  });
  rpcService.register('requestD2CSettings', () => {
    rpcService.notify('updateD2CSettings', d2cChatService.getSettings() || null);
  });
  rpcService.register('saveD2CSettings', (params) => {
    d2cChatService.saveSettings(params);
    rpcService.notify('updateD2CSettings', d2cChatService.getSettings());
  });

  rpcService.register('requestPlanningModel', async () => {
    const storageService = instantiationService.invokeFunction((accessor) => {
      return accessor.get(IStorageService);
    });

    const model = await storageService.get(PLANNING_MODEL_KEY);

    if (!model) {
      rpcService.notify('updatePlanningModel', PrdDefaultModelType);
    } else {
      rpcService.notify('updatePlanningModel', JSON.parse(model));
    }
  });
  rpcService.register('savePlanningModel', async (params: { modelType: string }) => {
    const storageService = instantiationService.invokeFunction((accessor) => {
      return accessor.get(IStorageService);
    });
    await storageService.set(PLANNING_MODEL_KEY, JSON.stringify(params));
    rpcService.notify('updatePlanningModel', params);
  });
  rpcService.register('requestCodingModel', async () => {
    const storageService = instantiationService.invokeFunction((accessor) => {
      return accessor.get(IStorageService);
    });

    const model = await storageService.get(CODING_MODEL_KEY);

    if (!model) {
      rpcService.notify('updateCodingModel', CodingDefaultModelType);
    } else {
      rpcService.notify('updateCodingModel', JSON.parse(model));
    }
  });
  rpcService.register('saveCodingModel', async (params: { modelType: string }) => {
    const storageService = instantiationService.invokeFunction((accessor) => {
      return accessor.get(IStorageService);
    });
    await storageService.set(CODING_MODEL_KEY, JSON.stringify(params));
    rpcService.notify('updateCodingModel', params);
  });

  rpcService.register('requestUnderstandingModel', async () => {
    const storageService = instantiationService.invokeFunction((accessor) => {
      return accessor.get(IStorageService);
    });

    const model = await storageService.get(UNDERSTANDING_MODEL_KEY);

    if (!model) {
      rpcService.notify('updateUnderstandingModel', UnderstandingDefaultModelType);
    } else {
      rpcService.notify('updateUnderstandingModel', JSON.parse(model));
    }
  });
  rpcService.register('saveUnderstandingModel', async (params: { modelType: string }) => {
    const storageService = instantiationService.invokeFunction((accessor) => {
      return accessor.get(IStorageService);
    });
    await storageService.set(UNDERSTANDING_MODEL_KEY, JSON.stringify(params));
    rpcService.notify('updateUnderstandingModel', params);
  });

  // 工具自动运行相关命令
  rpcService.register('requestToolAutoRun', async () => {
    const storageService = instantiationService.invokeFunction((accessor) => {
      return accessor.get(IStorageService);
    });

    const toolAutoRun = await storageService.get('toolAutoRun');

    if (!toolAutoRun) {
      rpcService.notify('updateToolAutoRun', { toolAutoRun: false });
    } else {
      rpcService.notify('updateToolAutoRun', { toolAutoRun: JSON.parse(toolAutoRun) });
    }
  });

  rpcService.register('saveToolAutoRun', async (params: { toolAutoRun: boolean }) => {
    const storageService = instantiationService.invokeFunction((accessor) => {
      return accessor.get(IStorageService);
    });
    await storageService.set('toolAutoRun', JSON.stringify(params.toolAutoRun));
    rpcService.notify('updateToolAutoRun', params);
  });

  rpcService.register('clearAllModelSettings', async () => {
    const storageService = instantiationService.invokeFunction((accessor) => {
      return accessor.get(IStorageService);
    });

    // 清除所有模型设置的存储
    await storageService.delete(PLANNING_MODEL_KEY);
    await storageService.delete(CODING_MODEL_KEY);
    await storageService.delete(UNDERSTANDING_MODEL_KEY);
    await storageService.delete('toolAutoRun'); // 新增

    // 通知前端重置为默认值
    rpcService.notify('updatePlanningModel', { modelType: PrdDefaultModelType });
    rpcService.notify('updateCodingModel', { modelType: CodingDefaultModelType });
    rpcService.notify('updateUnderstandingModel', { modelType: UnderstandingDefaultModelType });
    rpcService.notify('updateToolAutoRun', { toolAutoRun: false }); // 新增
  });

  rpcService.register('requestMcpSettings', () => {
    mcpHubService.sendLatestMcpServers();
  });
  rpcService.register('openMcpSettings', async () => {
    await mcpHubService.createMcpSettingsFileIfNeeded();
    commands.executeCommand('codin.webview.settings.openMcpSettings');
  });
  rpcService.register('requestRulesConfig', async () => {
    rpcService.notify('updateRulesConfig', rulesService.getRulesConfig());
  });
  rpcService.register('openRuleSettings', async () => {
    await rulesService.ensureRulesConfigJson();
    commands.executeCommand('codin.webview.settings.openRuleSettings');
  });
  rpcService.register('addRule', async (params) => {
    await rulesService.addRule(params);
    rpcService.notify('updateRulesConfig', rulesService.getRulesConfig());
  });
  rpcService.register('deleteRule', async (params) => {
    await rulesService.deleteRule(params);
    rpcService.notify('updateRulesConfig', rulesService.getRulesConfig());
  });
  rpcService.register('editRule', async (params) => {
    await rulesService.editRule(params);
    rpcService.notify('updateRulesConfig', rulesService.getRulesConfig());
  });
  rpcService.register('acceptAllDiffView', () => {
    commands.executeCommand('codin.webview.codediff.acceptAll');
  });
  rpcService.register('rejectAllDiffView', () => {
    commands.executeCommand('codin.webview.codediff.revertAll');
  });
  rpcService.register('openMarkdownRuleFile', (params) => {
    rulesService.openMarkdownRuleFile(params);
  });
  rpcService.register('showInformationMessage', (params) => {
    window.showInformationMessage(params.content, {
      detail: params.detail,
      modal: params.modal ?? false,
    });
  });
  rpcService.register('showErrorMessage', (params) => {
    window.showErrorMessage(params.content, {
      detail: params.detail,
      modal: params.modal ?? true,
    });
  });
  rpcService.register('reportSlardar', (params) => {
    slardarInstance?.report(params);
  });
  // uploadImage 留空，待后续实现
  rpcService.register('uploadImage', () => {
    // // 上传图片
    // commands.executeCommand('codin.webview.uploadImage')
  });
  rpcService.register('getRecentFiles', () => {
    return { list: workspaceFilesService.recentVisit };
  });
  rpcService.register('readFileContent', async (params) => {
    const content = await workspaceFilesService.readFileContent(params.path);
    return { content };
  });
  rpcService.register('readFolderListContent', async (params) => {
    const content = await workspaceFilesService.getFolderListContent(params.path);
    return { content };
  });
  rpcService.register('searchFile', async (params) => {
    const searchResult = await workspaceFilesService.searchFileOrDir(params.keyword);
    return { list: searchResult };
  });

  rpcService.register('planToolAccept', (params) => {
    planInteractor.accept(params.toolId);
  });

  rpcService.register('planToolReject', (params) => {
    planInteractor.reject(params.toolId);
  });

  rpcService.register('askUserToolConfirm', (params) => {
    askUserInteractor.confirmAnswer(params.toolId, params.answer);
  });
}
