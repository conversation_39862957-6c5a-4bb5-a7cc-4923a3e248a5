import { type IInstantiationService } from '@byted-image/lv-bedrock/di';
import { Disposable } from '@byted-image/lv-bedrock/dispose';

import { SendMessageParams, IPrdChatService } from '@/common/services/prd-chat/chat-service.interface';
import { ICodingChatService } from '@/common/services/coding-chat/coding-chat-service.interface';
import { ID2cChatService } from '@/common/services/d2c-chat/chat-service.interface';

import { IAccountService } from '@/common/services/account/account-service.interface';
import { IDiffViewService } from '@/common/services/diff-view/diff-view-service.interface';
import type { UserInput } from '@/bam/namespaces/userinput';
import { IPrivateChatService } from '@/private/private-chat-service.interface';
import type { Webview } from 'vscode';
import { registerRpcCommands } from './register-rpc-commands';
import { IRpcService } from '@/common/services/rpc/rpc-service.interface';
import { IUnderstandingChatService } from '@/common/services/understanding-chat/understanding-service.interface';
import { ClientMessage } from '@/conversation/client-message/abstract-message';
import { Int64 } from '@/bam';
import { IWorkspaceService } from '@/common/services/workspace/workspace.interface';
import { IFileLoggerService } from '@/common/services/file-logger/file-logger-service.interface';

interface IMessageChangeInfo {
  messages: ClientMessage[];
  type: 'add' | 'update';
  cid: string;
  parentCid?: string;
  parentMessageVersion?: Int64;
  isEof?: boolean;
}

export class Controller extends Disposable {
  private _instantiationService!: IInstantiationService;
  private _chatService!: IPrdChatService;
  private _codingChatService!: ICodingChatService;
  private _d2cChatService!: ID2cChatService;
  private _privateChatService!: IPrivateChatService;
  private _accountService!: IAccountService;
  private _diffViewService!: IDiffViewService;
  private _rpcService!: IRpcService;
  private _understandingChatService!: IUnderstandingChatService;
  private _workspaceService!: IWorkspaceService;
  private _loggerService!: IFileLoggerService;

  /**
   * 使用共享的DI容器初始化Controller
   * 这是推荐的初始化方式，确保服务实例的一致性
   */
  public initDI(instantiationService: IInstantiationService) {
    if (this._instantiationService) {
      return;
    }

    this._instantiationService = instantiationService;
    this._initServices();
  }

  private _initServices() {
    this._chatService = this._instantiationService.invokeFunction((accessor) => accessor.get(IPrdChatService));
    this._codingChatService = this._instantiationService.invokeFunction((accessor) => accessor.get(ICodingChatService));
    this._d2cChatService = this._instantiationService.invokeFunction((accessor) => accessor.get(ID2cChatService));
    this._understandingChatService = this._instantiationService.invokeFunction((accessor) =>
      accessor.get(IUnderstandingChatService),
    );
    this._privateChatService = this._instantiationService.invokeFunction((accessor) =>
      accessor.get(IPrivateChatService),
    );
    this._accountService = this._instantiationService.invokeFunction((accessor) => accessor.get(IAccountService));
    this._diffViewService = this._instantiationService.invokeFunction((accessor) => accessor.get(IDiffViewService));
    this._rpcService = this._instantiationService.invokeFunction((accessor) => accessor.get(IRpcService));
    this._workspaceService = this._instantiationService.invokeFunction((accessor) => accessor.get(IWorkspaceService));
    this._loggerService = this._instantiationService.invokeFunction((accessor) => accessor.get(IFileLoggerService));

    // 设置事件监听器
    this._register(
      this._chatService.onPresentAssistantMessage(async () => {
        console.log('[Controller-IChatService] onPresentAssistantMessage');
        await this._ensureRpcServiceReady();
        this._rpcService.notify('state', {
          page: 'techDocs',
          payload: JSON.stringify({
            messages: this._chatService.getMessages().map((message) => message.toJSON()),
          }),
        });
      }),
    );

    this._register(
      this._codingChatService.onPresentAssistantMessage(async () => {
        console.log('[Controller-ICodingChatService] onPresentAssistantMessage');
        await this._ensureRpcServiceReady();
        this._rpcService.notify('state', {
          page: 'coding',
          payload: JSON.stringify({
            messages: this._codingChatService.getMessages().map((message) => message.toJSON()),
          }),
        });
      }),
    );

    this._register(
      this._d2cChatService.onPresentAssistantMessage(async () => {
        console.log('[Controller-ID2cChatService] onPresentAssistantMessage');
        await this._ensureRpcServiceReady();
        this._rpcService.notify('state', {
          page: 'd2c',
          payload: JSON.stringify({
            messages: this._d2cChatService.getMessages().map((message) => message.toJSON()),
          }),
        });
      }),
    );

    this._register(
      this._understandingChatService.onPresentAssistantMessage(async () => {
        console.log('[Controller-IUnderstandingChatService] onPresentAssistantMessage');
        await this._ensureRpcServiceReady();
        this._rpcService.notify('state', {
          page: 'understanding',
          payload: JSON.stringify({
            messages: this._understandingChatService.getMessages().map((message) => message.toJSON()),
          }),
        });
      }),
    );

    this._register(
      this._privateChatService.onPresentAssistantMessage(async () => {
        console.log('[Controller] onPresentAssistantMessage');
        await this._ensureRpcServiceReady();
        this._rpcService.notify('state', {
          page: 'private',
          payload: JSON.stringify({
            messages: this._privateChatService.getMessages().map((message) => message.toJSON()),
          }),
        });
      }),
    );

    if (this._accountService.hasLogin) {
      this._ensureRpcServiceReady().then(() => {
        this._rpcService.notify('updateUserInfo', this._accountService.getUserInfo() || null);
      });
    }
    this._register(
      this._accountService.onDidLogin(async () => {
        await this._ensureRpcServiceReady();
        this._rpcService.notify('updateUserInfo', this._accountService.getUserInfo() || null);
      }),
    );
    this._register(
      this._accountService.onDidLogout(async () => {
        await this._ensureRpcServiceReady();
        this._rpcService.notify('updateUserInfo', null);
      }),
    );

    this._register(
      this._diffViewService.onChangeIsEditing(async () => {
        await this._ensureRpcServiceReady();
        this._rpcService.notify('updateDiffViewIsEditing', this._diffViewService.isEditing);
      }),
    );

    this._register(
      this._codingChatService.onConversationMessageChange((messageChangeInfo: IMessageChangeInfo) => {
        this._rpcService.notify('diffInfo', {
          ...messageChangeInfo,
          isEof: messageChangeInfo.isEof,
        });
      }),
    );
  }

  public resetConversationServices() {
    console.log('[Controller] 重置所有会话服务状态');
    this._chatService.getMessages(); // 触发服务初始化
    this._codingChatService.getMessages(); // 触发服务初始化
    this._d2cChatService.getMessages(); // 触发服务初始化

    this._chatService.resetMessageReceiverAndSocket();
    this._codingChatService.resetMessageReceiverAndSocket();
    this._d2cChatService.resetMessageReceiverAndSocket();
  }

  /**
   * 获取当前会话服务的实时状态
   * @param conversationType 会话类型
   */
  public async getCurrentConversationState(conversationType?: string, conversationId?: string) {
    console.log('[Controller] 获取当前会话状态，类型:', conversationType, conversationId);

    const service = this._instantiationService.invokeFunction((accessor) => {
      // 根据会话类型选择对应的服务
      switch (conversationType) {
        case 'techDocs':
          return accessor.get(IPrdChatService);
        case 'd2c':
          return accessor.get(ID2cChatService);
        default:
          return accessor.get(ICodingChatService);
      }
    });

    await service.switchCurrentConversation(conversationId);
    // 获取当前会话的消息和状态
    const messages = service.getMessages();
    const result = await service.getCurrentContextState();

    const currentConversationId = conversationId ?? service.currentCid;
    const currentConversationType = conversationType;

    console.log('[Controller] 当前会话状态:', {
      currentConversationId,
      conversationType: currentConversationType,
      messageCount: messages.length,
    });

    // 发送当前会话状态给webview
    this._rpcService.notify('currentConversationState', {
      conversationId: currentConversationId,
      conversationType: currentConversationType || conversationType,
      // biome-ignore lint/suspicious/noExplicitAny: TODO: chezongshao
      messages: messages.map((message: any) =>
        message.toJSON
          ? message.toJSON()
          : {
              role: message.role || (message.sender === 'User' ? 'user' : 'assistant'),
              content: message.content || message.text || '',
              text: message.content || message.text || '',
              sender: message.sender || (message.role === 'user' ? 'User' : 'Assistant'),
            },
      ),
      contextState: result,
    });
  }

  /**
   * 设置 webview 实例，用于 RPC 通信
   */
  public setWebview(webview: Webview) {
    this._rpcService.setWebview(webview);
    // 注册所有 RPC 命令
    registerRpcCommands(this, this._instantiationService);
  }

  private async _ensureRpcServiceReady() {
    await this._rpcService.initialize();
  }

  public sendTechDocsMessage(userInput: UserInput, params?: SendMessageParams) {
    this._chatService.sendMessage(
      {
        role: 0,
        userContent: userInput,
      },
      params,
    );
  }

  public sendCodingMessage(userInput: UserInput, cid?: string) {
    this._codingChatService.sendMessage({
      cid,
      role: 0,
      userContent: userInput,
    });
  }

  public sendUnderstandingMessage(userInput: UserInput, cid?: string) {
    this._understandingChatService.sendMessage({
      cid,
      role: 0,
      userContent: userInput,
    });
  }

  public sendD2cMessage(userInput: UserInput) {
    this._d2cChatService.sendMessage({
      role: 0,
      userContent: userInput,
    });
  }

  public sendPrivateMessage(userInput: UserInput) {
    this._privateChatService.sendMessage({
      role: 0,
      userContent: userInput,
    });
  }

  public async setConversationBusiness(cid: string, business: string) {
    await this._codingChatService.setConversationBusiness(cid, business);
    this._loggerService.info(`[Controller] 设置会话业务线成功: ${cid}, ${business}`);
  }

  public async getConversationBusiness(cid: string) {
    const business = await this._codingChatService.getConversationBusiness(cid);
    this._loggerService.info(`[Controller] 获取会话业务线成功: ${cid}, ${business}`);
    return business;
  }

  public getConversationBusinessList() {
    const businessList = this._workspaceService.getBusinessList();
    this._loggerService.info(`[Controller] 获取会话业务线列表成功: ${businessList}`);
    return businessList;
  }
}
