{"version": "0.2.0", "configurations": [{"name": "Run Agent Extension", "type": "extensionHost", "request": "launch", "args": ["--disable-extensions", "--extensionDevelopmentPath=${workspaceFolder}/agent-extension"], "outFiles": ["${workspaceFolder}/agent-extension/out/**/*.js"], "preLaunchTask": "${defaultBuildTask}"}, {"name": "Extension Tests", "type": "extensionHost", "request": "launch", "args": ["--extensionDevelopmentPath=${workspaceFolder}/agent-extension", "--extensionTestsPath=${workspaceFolder}/agent-extension/out/test/suite/index"], "outFiles": ["${workspaceFolder}/agent-extension/out/test/**/*.js"], "preLaunchTask": "${defaultBuildTask}"}]}