{"editor.defaultFormatter": "biomejs.biome", "biome.lsp.bin": "./web/node_modules/@biomejs/biome/bin/biome", "typescript.preferences.preferTypeOnlyAutoImports": true, "typescript.tsserver.maxTsServerMemory": 32768, "[typescript]": {"editor.defaultFormatter": "biomejs.biome"}, "[javascript]": {"editor.defaultFormatter": "biomejs.biome"}, "[typescriptreact]": {"editor.defaultFormatter": "biomejs.biome"}, "[javascriptreact]": {"editor.defaultFormatter": "biomejs.biome"}, "[scss]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[css]": {"editor.defaultFormatter": "biomejs.biome"}, "[json]": {"editor.defaultFormatter": "biomejs.biome"}, "[cpp]": {"editor.defaultFormatter": "ms-vscode.cpptools"}, "[python]": {"editor.defaultFormatter": "ms-python.python"}, "[html]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "editor.formatOnSave": true, "editor.tabSize": 2, "editor.codeActionsOnSave": {"quickfix.biome": "explicit"}, "cSpell.words": ["aigc", "byted", "Evercloud", "Hypic", "Thirdparty", "tiktok", "<PERSON><PERSON>"], "files.associations": {"*.gyp": "json", "array": "cpp", "atomic": "cpp", "bit": "cpp", "*.tcc": "cpp", "bitset": "cpp", "cctype": "cpp", "chrono": "cpp", "cinttypes": "cpp", "clocale": "cpp", "cmath": "cpp", "compare": "cpp", "concepts": "cpp", "condition_variable": "cpp", "cstdarg": "cpp", "cstddef": "cpp", "cstdint": "cpp", "cstdio": "cpp", "cstdlib": "cpp", "cstring": "cpp", "ctime": "cpp", "cwchar": "cpp", "cwctype": "cpp", "deque": "cpp", "forward_list": "cpp", "list": "cpp", "map": "cpp", "set": "cpp", "unordered_map": "cpp", "vector": "cpp", "exception": "cpp", "algorithm": "cpp", "functional": "cpp", "iterator": "cpp", "memory": "cpp", "memory_resource": "cpp", "numeric": "cpp", "optional": "cpp", "random": "cpp", "ratio": "cpp", "regex": "cpp", "string": "cpp", "string_view": "cpp", "system_error": "cpp", "tuple": "cpp", "type_traits": "cpp", "utility": "cpp", "initializer_list": "cpp", "iosfwd": "cpp", "iostream": "cpp", "istream": "cpp", "limits": "cpp", "mutex": "cpp", "new": "cpp", "ostream": "cpp", "ranges": "cpp", "sstream": "cpp", "stdexcept": "cpp", "stop_token": "cpp", "streambuf": "cpp", "thread": "cpp", "typeinfo": "cpp", "valarray": "cpp", "complex": "cpp", "unordered_set": "cpp", "fstream": "cpp", "iomanip": "cpp"}, "editor.rulers": [120], "go.formatTool": "gofmt", "go.useLanguageServer": true, "[go]": {"editor.defaultFormatter": "golang.go", "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.organizeImports": "explicit"}}, "git.ignoreLimitWarning": true}